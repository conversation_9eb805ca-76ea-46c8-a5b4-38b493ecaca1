package main

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

// SongInfo 歌曲信息结构
type SongInfo struct {
	RowIndex    int
	SongCode    string
	SongName    string
	Artist      string
	CallCount   int64  // 调用次数
	OriginalRow []interface{} // 原始行数据
}

// PlayNumInfo 播放量信息结构
type PlayNumInfo struct {
	SongCode string
	PlayNum  int64
}

// ReadSongList 读取唱鸭案涉歌曲清单
func ReadSongList(filePath string) ([]SongInfo, []string, error) {
	fmt.Printf("正在读取歌曲清单文件: %s\n", filePath)
	
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, nil, fmt.Errorf("error opening song list file: %w", err)
	}
	defer f.Close()

	// 获取第一个工作表
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, nil, fmt.Errorf("error reading rows: %w", err)
	}

	if len(rows) == 0 {
		return nil, nil, fmt.Errorf("file is empty")
	}

	// 找到真正的表头行（通常在第3行，索引为2）
	var headers []string
	var headerRowIndex int = -1

	for i, row := range rows {
		if len(row) > 0 {
			// 检查是否包含"songcode"等关键字
			rowStr := strings.ToLower(strings.Join(row, " "))
			if strings.Contains(rowStr, "songcode") || strings.Contains(rowStr, "歌曲名称") {
				headers = row
				headerRowIndex = i
				break
			}
		}
	}

	if headerRowIndex == -1 {
		return nil, nil, fmt.Errorf("未找到表头行")
	}

	fmt.Printf("表头行索引: %d\n", headerRowIndex)
	fmt.Printf("表头: %v\n", headers)

	// 查找关键列的索引
	songCodeCol := -1
	songNameCol := -1
	artistCol := -1
	callCountCol := -1

	for i, header := range headers {
		headerLower := strings.ToLower(strings.TrimSpace(header))
		if strings.Contains(headerLower, "song_code") || strings.Contains(headerLower, "songcode") || strings.Contains(headerLower, "歌曲代码") {
			songCodeCol = i
		} else if strings.Contains(headerLower, "song_name") || strings.Contains(headerLower, "歌曲名称") || strings.Contains(headerLower, "歌名") {
			songNameCol = i
		} else if strings.Contains(headerLower, "artist") || strings.Contains(headerLower, "歌手") || strings.Contains(headerLower, "演唱者") {
			artistCol = i
		} else if strings.Contains(headerLower, "调用次数") || strings.Contains(headerLower, "播放量") || strings.Contains(headerLower, "call_count") {
			callCountCol = i
		}
	}

	fmt.Printf("列索引 - SongCode: %d, SongName: %d, Artist: %d, CallCount: %d\n", 
		songCodeCol, songNameCol, artistCol, callCountCol)

	if songCodeCol == -1 {
		return nil, nil, fmt.Errorf("未找到歌曲代码列")
	}

	// 读取数据行（从表头行的下一行开始）
	var songs []SongInfo
	for i := headerRowIndex + 1; i < len(rows); i++ {
		row := rows[i]
		if len(row) <= songCodeCol {
			continue
		}

		songCodeRaw := strings.TrimSpace(row[songCodeCol])
		if songCodeRaw == "" {
			continue
		}

		// 处理可能包含多个songcode的情况（用分号分隔）
		songCodes := strings.Split(songCodeRaw, "；")
		if len(songCodes) == 1 {
			songCodes = strings.Split(songCodeRaw, ";") // 也尝试英文分号
		}

		// 取第一个songcode作为主要的
		songCode := strings.TrimSpace(songCodes[0])
		if songCode == "" {
			continue
		}

		song := SongInfo{
			RowIndex: i + 1, // Excel行号（从1开始）
			SongCode: songCode,
		}

		// 读取歌曲名称
		if songNameCol != -1 && len(row) > songNameCol {
			song.SongName = strings.TrimSpace(row[songNameCol])
		}

		// 读取歌手
		if artistCol != -1 && len(row) > artistCol {
			song.Artist = strings.TrimSpace(row[artistCol])
		}

		// 读取现有调用次数
		if callCountCol != -1 && len(row) > callCountCol {
			callCountStr := strings.TrimSpace(row[callCountCol])
			if callCountStr != "" {
				if count, err := strconv.ParseInt(callCountStr, 10, 64); err == nil {
					song.CallCount = count
				}
			}
		}

		// 保存原始行数据
		song.OriginalRow = make([]interface{}, len(row))
		for j, cell := range row {
			song.OriginalRow[j] = cell
		}

		songs = append(songs, song)
	}

	fmt.Printf("读取到 %d 首歌曲\n", len(songs))
	return songs, headers, nil
}

// ReadPlayNumData 读取播放量数据
func ReadPlayNumData(filePath string) (map[string]int64, error) {
	fmt.Printf("正在读取播放量数据文件: %s\n", filePath)
	
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("error opening play num file: %w", err)
	}
	defer f.Close()

	// 获取第一个工作表
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("error reading rows: %w", err)
	}

	if len(rows) == 0 {
		return nil, fmt.Errorf("file is empty")
	}

	// 读取表头
	headers := rows[0]
	fmt.Printf("播放量文件表头: %v\n", headers)

	// 查找关键列的索引
	songCodeCol := -1
	playNumCol := -1

	for i, header := range headers {
		headerLower := strings.ToLower(strings.TrimSpace(header))
		if strings.Contains(headerLower, "song_code") || strings.Contains(headerLower, "songcode") {
			songCodeCol = i
		} else if strings.Contains(headerLower, "play_num") || strings.Contains(headerLower, "playnum") || strings.Contains(headerLower, "播放量") {
			playNumCol = i
		}
	}

	fmt.Printf("播放量文件列索引 - SongCode: %d, PlayNum: %d\n", songCodeCol, playNumCol)

	if songCodeCol == -1 || playNumCol == -1 {
		return nil, fmt.Errorf("未找到必要的列（songcode或playnum）")
	}

	// 读取数据
	playNumMap := make(map[string]int64)
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		if len(row) <= songCodeCol || len(row) <= playNumCol {
			continue
		}

		songCode := strings.TrimSpace(row[songCodeCol])
		playNumStr := strings.TrimSpace(row[playNumCol])

		if songCode == "" || playNumStr == "" {
			continue
		}

		playNum, err := strconv.ParseInt(playNumStr, 10, 64)
		if err != nil {
			fmt.Printf("警告: 无法解析播放量 '%s' for songcode %s\n", playNumStr, songCode)
			continue
		}

		playNumMap[songCode] = playNum
	}

	fmt.Printf("读取到 %d 个歌曲的播放量数据\n", len(playNumMap))
	return playNumMap, nil
}

// MatchAndUpdateSongs 匹配并更新歌曲调用次数
func MatchAndUpdateSongs(songs []SongInfo, playNumMap map[string]int64) []SongInfo {
	fmt.Println("正在匹配歌曲代码和播放量...")
	
	matchedCount := 0
	unmatchedCount := 0
	var unmatchedSongs []string

	for i := range songs {
		if playNum, exists := playNumMap[songs[i].SongCode]; exists {
			songs[i].CallCount = playNum
			matchedCount++
		} else {
			unmatchedCount++
			unmatchedSongs = append(unmatchedSongs, songs[i].SongCode)
		}
	}

	fmt.Printf("匹配结果: 成功匹配 %d 首, 未匹配 %d 首\n", matchedCount, unmatchedCount)
	
	if unmatchedCount > 0 && unmatchedCount <= 10 {
		fmt.Printf("未匹配的歌曲代码: %v\n", unmatchedSongs)
	} else if unmatchedCount > 10 {
		fmt.Printf("未匹配的歌曲代码（前10个）: %v\n", unmatchedSongs[:10])
	}

	return songs
}

// WriteUpdatedSongList 写入更新后的歌曲清单
func WriteUpdatedSongList(songs []SongInfo, headers []string, outputPath string) error {
	fmt.Printf("正在写入更新后的文件: %s\n", outputPath)
	
	f := excelize.NewFile()
	sheetName := f.GetSheetName(0)

	// 查找调用次数列的索引
	callCountCol := -1
	for i, header := range headers {
		headerLower := strings.ToLower(strings.TrimSpace(header))
		if strings.Contains(headerLower, "调用次数") || strings.Contains(headerLower, "播放量") || strings.Contains(headerLower, "call_count") {
			callCountCol = i
			break
		}
	}

	// 如果没有找到调用次数列，添加一个
	if callCountCol == -1 {
		headers = append(headers, "调用次数")
		callCountCol = len(headers) - 1
	}

	// 写入表头
	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// 写入数据
	for i, song := range songs {
		row := i + 2 // Excel行号从2开始（第1行是表头）
		
		// 写入原始数据
		for j, cellValue := range song.OriginalRow {
			if j < len(headers)-1 || callCountCol < len(song.OriginalRow) {
				cell, _ := excelize.CoordinatesToCellName(j+1, row)
				f.SetCellValue(sheetName, cell, cellValue)
			}
		}
		
		// 写入调用次数
		callCountCell, _ := excelize.CoordinatesToCellName(callCountCol+1, row)
		f.SetCellValue(sheetName, callCountCell, song.CallCount)
	}

	// 设置列宽
	for i := 0; i < len(headers); i++ {
		colName, _ := excelize.ColumnNumberToName(i + 1)
		f.SetColWidth(sheetName, colName, colName, 15)
	}

	// 保存文件
	err := f.SaveAs(outputPath)
	if err != nil {
		return fmt.Errorf("error saving file: %w", err)
	}

	return nil
}

// MatchSongCodePlayNumEnter 主入口函数
func MatchSongCodePlayNumEnter() error {
	fmt.Println("开始匹配歌曲代码和播放量...")

	// 文件路径
	songListPath := "output/唱鸭案涉歌曲清单（6143号案）.xlsx"
	playNumPath := "output/SongCode_Analysis/songcode_total_playnum_20250617_142501.xlsx"

	// 1. 读取歌曲清单
	songs, headers, err := ReadSongList(songListPath)
	if err != nil {
		return fmt.Errorf("error reading song list: %w", err)
	}

	// 2. 读取播放量数据
	playNumMap, err := ReadPlayNumData(playNumPath)
	if err != nil {
		return fmt.Errorf("error reading play num data: %w", err)
	}

	// 3. 匹配并更新
	updatedSongs := MatchAndUpdateSongs(songs, playNumMap)

	// 4. 生成输出文件路径
	timestamp := time.Now().Format("20060102_150405")
	outputPath := filepath.Join("output", fmt.Sprintf("唱鸭案涉歌曲清单_已匹配播放量_%s.xlsx", timestamp))

	// 5. 写入更新后的文件
	err = WriteUpdatedSongList(updatedSongs, headers, outputPath)
	if err != nil {
		return fmt.Errorf("error writing updated song list: %w", err)
	}

	// 6. 打印统计信息
	fmt.Println("\n=== 匹配统计信息 ===")
	fmt.Printf("总歌曲数: %d\n", len(updatedSongs))
	
	totalPlayNum := int64(0)
	matchedCount := 0
	for _, song := range updatedSongs {
		if song.CallCount > 0 {
			matchedCount++
			totalPlayNum += song.CallCount
		}
	}
	
	fmt.Printf("有播放量的歌曲: %d\n", matchedCount)
	fmt.Printf("无播放量的歌曲: %d\n", len(updatedSongs)-matchedCount)
	fmt.Printf("总播放量: %d\n", totalPlayNum)
	fmt.Printf("输出文件: %s\n", outputPath)

	fmt.Println("\n匹配完成！")
	return nil
}
