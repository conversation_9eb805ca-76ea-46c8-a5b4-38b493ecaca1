package main

import (
	"compress/gzip"
	"encoding/csv"
	"fmt"
	"github.com/xuri/excelize/v2"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

// DailyPlayNumRecord 表示每日播放量统计记录
type DailyPlayNumRecord struct {
	SongCode string
	Date     string
	PlayNum  int64
}

// SongCodePlayNumSummary 表示歌曲代码播放量汇总
type SongCodePlayNumSummary struct {
	SongCode      string
	TotalPlayNum  int64
	DailyPlayNums map[string]int64 // 日期 -> 播放量
}

// FindGzipFiles 查找指定目录下的所有 .csv.gz 文件
func FindGzipFiles(targetDir string) ([]string, error) {
	var gzipFiles []string

	// 使用 filepath.Walk 遍历目录
	err := filepath.Walk(targetDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 检查文件是否以 .csv.gz 结尾
		if !info.IsDir() && strings.HasSuffix(strings.ToLower(info.Name()), ".csv.gz") {
			gzipFiles = append(gzipFiles, path)
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("error walking directory %s: %w", targetDir, err)
	}

	return gzipFiles, nil
}

// DecompressGzipFile 解压 gzip 文件并返回 CSV reader
func DecompressGzipFile(gzipFilePath string) (*csv.Reader, *os.File, *gzip.Reader, error) {
	// 打开 gzip 文件
	file, err := os.Open(gzipFilePath)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("error opening gzip file %s: %w", gzipFilePath, err)
	}

	// 创建 gzip reader
	gzipReader, err := gzip.NewReader(file)
	if err != nil {
		file.Close()
		return nil, nil, nil, fmt.Errorf("error creating gzip reader for %s: %w", gzipFilePath, err)
	}

	// 创建 CSV reader
	csvReader := csv.NewReader(gzipReader)

	return csvReader, file, gzipReader, nil
}

// ConvertMillisToDate 将毫秒时间戳转换为日期字符串 (YYYY-MM-DD)
func ConvertMillisToDate(millisStr string) (string, error) {
	if millisStr == "" {
		return "", fmt.Errorf("empty timestamp")
	}

	// 转换为整数
	millis, err := strconv.ParseInt(millisStr, 10, 64)
	if err != nil {
		return "", fmt.Errorf("error parsing timestamp %s: %w", millisStr, err)
	}

	// 转换为时间
	t := time.Unix(millis/1000, (millis%1000)*1000000)

	// 转换为UTC时间并格式化为日期
	return t.UTC().Format("2006-01-02"), nil
}

// ProcessGzipFilesForSongCodes 处理 gzip 文件，统计指定 songCodes 的播放量
func ProcessGzipFilesForSongCodes(gzipFiles []string, songCodes []string) (map[string]*SongCodePlayNumSummary, error) {
	// 创建 songCode 查找映射
	songCodeMap := make(map[string]bool)
	for _, code := range songCodes {
		songCodeMap[code] = true
	}

	// 结果映射
	results := make(map[string]*SongCodePlayNumSummary)

	// 处理每个 gzip 文件
	for _, gzipFile := range gzipFiles {
		fmt.Printf("Processing file: %s\n", gzipFile)

		// 解压文件
		csvReader, file, gzipReader, err := DecompressGzipFile(gzipFile)
		if err != nil {
			fmt.Printf("Warning: failed to decompress %s: %v\n", gzipFile, err)
			continue
		}

		// 处理单个文件的函数
		func() {
			// 确保资源被释放
			defer func() {
				gzipReader.Close()
				file.Close()
			}()

			// 读取表头
			header, err := csvReader.Read()
			if err != nil {
				fmt.Printf("Warning: failed to read header from %s: %v\n", gzipFile, err)
				return
			}

			// 查找列索引
			songCodeIndex := -1
			playNumIndex := -1
			ltsIndex := -1

			for i, col := range header {
				switch strings.ToLower(strings.TrimSpace(col)) {
				case "songcode":
					songCodeIndex = i
				case "playnum":
					playNumIndex = i
				case "lts":
					ltsIndex = i
				}
			}

			if songCodeIndex == -1 || playNumIndex == -1 || ltsIndex == -1 {
				fmt.Printf("Warning: required columns not found in %s (songCode: %d, playNum: %d, lts: %d)\n",
					gzipFile, songCodeIndex, playNumIndex, ltsIndex)
				return
			}

			// 读取数据行
			for {
				record, err := csvReader.Read()
				if err == io.EOF {
					break
				}
				if err != nil {
					fmt.Printf("Warning: error reading record from %s: %v\n", gzipFile, err)
					continue
				}

				// 检查记录长度
				if len(record) <= songCodeIndex || len(record) <= playNumIndex || len(record) <= ltsIndex {
					continue
				}

				songCode := strings.TrimSpace(record[songCodeIndex])
				playNumStr := strings.TrimSpace(record[playNumIndex])
				ltsStr := strings.TrimSpace(record[ltsIndex])

				// 检查是否是目标 songCode
				if !songCodeMap[songCode] {
					continue
				}

				// 解析 playNum
				playNum, err := strconv.ParseInt(playNumStr, 10, 64)
				if err != nil {
					continue // 跳过无效的 playNum
				}

				// 转换时间戳为日期
				date, err := ConvertMillisToDate(ltsStr)
				if err != nil {
					continue // 跳过无效的时间戳
				}

				// 更新统计结果
				if results[songCode] == nil {
					results[songCode] = &SongCodePlayNumSummary{
						SongCode:      songCode,
						TotalPlayNum:  0,
						DailyPlayNums: make(map[string]int64),
					}
				}

				results[songCode].TotalPlayNum += playNum
				results[songCode].DailyPlayNums[date] += playNum
			}
		}()
	}

	return results, nil
}

// ExportSongCodePlayNumToExcel 将统计结果导出到 Excel 文件
func ExportSongCodePlayNumToExcel(results map[string]*SongCodePlayNumSummary, outputPath string) error {
	// 创建 Excel 文件
	f := excelize.NewFile()

	// 获取默认工作表名称
	sheetName := f.GetSheetName(0)

	// 设置表头
	headers := []string{"SongCode", "Date", "PlayNum", "TotalPlayNum"}
	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// 写入数据
	row := 2
	for _, summary := range results {
		// 为每个日期写入一行
		for date, playNum := range summary.DailyPlayNums {
			f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), summary.SongCode)
			f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), date)
			f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), playNum)
			f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), summary.TotalPlayNum)
			row++
		}
	}

	// 设置列宽
	f.SetColWidth(sheetName, "A", "A", 20) // SongCode
	f.SetColWidth(sheetName, "B", "B", 12) // Date
	f.SetColWidth(sheetName, "C", "C", 12) // PlayNum
	f.SetColWidth(sheetName, "D", "D", 15) // TotalPlayNum

	// 保存文件
	return f.SaveAs(outputPath)
}

// ExportSongCodePlayNumSummaryToExcel 将汇总结果导出到 Excel 文件
func ExportSongCodePlayNumSummaryToExcel(results map[string]*SongCodePlayNumSummary, outputPath string) error {
	// 创建 Excel 文件
	f := excelize.NewFile()

	// 获取默认工作表名称
	sheetName := f.GetSheetName(0)

	// 设置表头
	f.SetCellValue(sheetName, "A1", "SongCode")
	f.SetCellValue(sheetName, "B1", "TotalPlayNum")

	// 写入数据
	row := 2
	for _, summary := range results {
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), summary.SongCode)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), summary.TotalPlayNum)
		row++
	}

	// 设置列宽
	f.SetColWidth(sheetName, "A", "A", 20) // SongCode
	f.SetColWidth(sheetName, "B", "B", 15) // TotalPlayNum

	// 保存文件
	return f.SaveAs(outputPath)
}

// ExportSongCodePlayNumFromFileEnter 主入口函数：根据传入的 songCodes 从文件统计播放量
func ExportSongCodePlayNumFromFileEnter() error {
	songCodes := []int64{6625526682240150,
		6654550181453460,
		6315145763604900,
		6654550229033190,
		6625526695123630,
		6387856989459740,
		6315145531735290,
		6625526739778360,
		6388462585468140,
		6843908891163350,
		6625526797509980,
		6654550212463820,
		6654550106832910,
		6625526871745940,
		6654550128826440,
		6654550120438200,
		6625526747194040,
		6625526945047550,
		6654550108375580,
		6625526989292290,
		6654550229582030,
		6654550170429460,
		6625526854867110,
		6625526879145010,
		6625526811311330,
		6625526676697570,
		6315145643972570,
		6625526651824460,
		6625526680986690,
		6388424384610470,
		6315145734036540,
		6625526703695850,
		6315145610459510,
		6625526771252030,
		6315145692146230,
		6654550212577040,
		6625526945271940,
		6388469425377430,
		6625526660721950,
		6315145546971630,
		6625526775817960,
		6654550130924080,
		6625526641218430,
		6625526719386250,
		6625526638168960,
		6625526765548690,
		6388416069358340,
		6315145554122440,
		6625526648214030,
		6625526656717500,
		6625526810839550,
		6388464828201640,
		6625526805496240,
		6625526644683410,
		6625526799528570,
		6625526667993550,
		6625526739925210,
		6654550108437270,
		6625526752806480,
		6654550181446950,
		6625526757415700,
		6654550228443260,
		6625526619484580,
		6315145525028630,
		6625526610655890,
		6387813379652630,
		6315145500685550,
		6625526685722560,
		6315145610252520,
		6625526638566120,
		6654550228539200,
		6625526610034330,
		6625526620457440,
		6387830145065530,
		6625526612665740,
		6387827590815060,
		6654550148104360,
		6654550123335160,
		6654550128961820,
		6625526716623900,
		6654550133161490,
		6625526644264230,
		6625526646758230,
		6387819464386020,
		6625526648840320,
		6625526715899220,
		6387833867799670,
		6246262727325690,
		6625526699204620,
		6625526901987680,
		6625526725785270,
		6625526901991500,
		6625526699117260,
		6388415954350510,
		6625526625669790,
		6315145716182660,
		6625526743869780,
		6625526901936300,
		6654550267032730,
		6625526603579120,
		6246262727285680,
		6625526806728900,
		6654550266740830,
		6625526605875460,
		6246262727304010,
		6654550225935700,
		6625526811968440,
		6625526755317260,
		6654550225325120,
		6625526795114120,
		6625526982770580,
		6625526811383940,
		6625526652938770,
		6315145546967260,
		6625526689633920,
		6625526916464030,
		6625526640700260,
		6315145509762020,
		6654550223178030,
		6625526700816370,
		6625526649579560,
		6625526668625960,
		6625526760170160,
		6315145671778900,
		6654550211983460,
		6654550213596920,
		6625526725789240,
		6625526901983420,
		6654550153422290,
		6625526985308730,
		6625526731342650,
		6625526810726580,
		6315145515555740,
		6625527011244580,
		6625526638224960,
		6315145574627490,
		6625526874193840,
		6654550175763890,
		6315145610106370,
		6625526795319520,
		6625526766007010,
		6625526901972370,
		6625526733804080,
		6625526683529270,
		6315145827653010,
		6315145771574570,
		6625526754755230,
		6654550269431800,
		6387918747892210,
		6246262727321390,
		6654550128824020,
		6625526945268710,
		6625526783191730,
		6387875040172960,
		6625526864372690,
		6315145694495740,
		6625526608351780,
		6387830963237720,
		6246262727322921,
		6625526625018040,
		6625526608909220,
		6625526603797270,
		6387805158554710,
		6246262727304491,
		6625526605404050,
		6625526989101750,
		6625526794361360,
		6654550226827970,
		6625526796660940,
		6625526634676370,
		6246262727324200,
		6654550175766390,
		6388089959531600,
		6315145705913450,
		6625526613533040,
		6387805946161410,
		6315145553058580,
		6625527018185600,
		6625526758194740,
		6625526939207750,
		6625526702886340,
		6388461285255300,
		6315145693232520,
		6654550227251210,
		6625526716407220,
		6625526688755140,
		6625526716187980,
		6654550111321840,
		6625526901768740,
		6625526863294430,
		6625526615806550,
		6654550130775130,
		6625526844544990,
		6654550227554200,
		6625526904653140,
		6375715356697110,
		6625526770401430,
		6654550127581880,
		6625526619672450,
		6654550218332660,
		6625526610548820,
		6654550106836600,
		6625526731619110,
		6625526656855990,
		6387825203879320,
		6315145542270830,
		6625526682041540,
		6315145546961510,
		6654550175673540,
		6315145681567740}
	// 将 int64 数组转换为字符串数组
	songCodeStrings := make([]string, len(songCodes))
	for i, code := range songCodes {
		songCodeStrings[i] = fmt.Sprintf("%d", code)
	}

	// 定义目标目录
	targetDir := `E:\music_import\history`

	fmt.Printf("Starting to process song codes from files in directory: %s\n", targetDir)
	fmt.Printf("Target song codes count: %d\n", len(songCodes))

	// 查找所有 .csv.gz 文件
	fmt.Println("Finding .csv.gz files...")
	gzipFiles, err := FindGzipFiles(targetDir)
	if err != nil {
		return fmt.Errorf("error finding gzip files: %w", err)
	}

	fmt.Printf("Found %d .csv.gz files\n", len(gzipFiles))
	if len(gzipFiles) == 0 {
		return fmt.Errorf("no .csv.gz files found in directory: %s", targetDir)
	}

	// 处理文件并统计播放量
	fmt.Println("Processing files and calculating play numbers...")
	results, err := ProcessGzipFilesForSongCodes(gzipFiles, songCodeStrings)
	if err != nil {
		return fmt.Errorf("error processing gzip files: %w", err)
	}

	fmt.Printf("Successfully processed files. Found data for %d song codes\n", len(results))

	// 创建输出目录
	outputDir := "output"
	if _, err := os.Stat(outputDir); os.IsNotExist(err) {
		err = os.Mkdir(outputDir, 0755)
		if err != nil {
			return fmt.Errorf("error creating output directory: %w", err)
		}
	}

	// 生成时间戳
	timestamp := time.Now().Format("20060102_150405")

	// 导出详细结果（包含每日播放量）
	detailOutputPath := filepath.Join(outputDir, fmt.Sprintf("songcode_daily_playnum_%s.xlsx", timestamp))
	fmt.Printf("Exporting detailed results to: %s\n", detailOutputPath)
	err = ExportSongCodePlayNumToExcel(results, detailOutputPath)
	if err != nil {
		return fmt.Errorf("error exporting detailed results: %w", err)
	}

	// 导出汇总结果（只包含总播放量）
	summaryOutputPath := filepath.Join(outputDir, fmt.Sprintf("songcode_total_playnum_%s.xlsx", timestamp))
	fmt.Printf("Exporting summary results to: %s\n", summaryOutputPath)
	err = ExportSongCodePlayNumSummaryToExcel(results, summaryOutputPath)
	if err != nil {
		return fmt.Errorf("error exporting summary results: %w", err)
	}

	// 打印统计信息
	fmt.Println("\n=== Statistics Summary ===")
	totalPlayNum := int64(0)
	for songCode, summary := range results {
		fmt.Printf("SongCode: %s, Total PlayNum: %d, Days with data: %d\n",
			songCode, summary.TotalPlayNum, len(summary.DailyPlayNums))
		totalPlayNum += summary.TotalPlayNum
	}
	fmt.Printf("Overall Total PlayNum: %d\n", totalPlayNum)

	fmt.Println("Export completed successfully!")
	return nil
}
