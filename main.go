package main

import (
	"database/sql"
	"fmt"
	_ "github.com/go-sql-driver/mysql"
)

// 生成一个 数据库连接
func CreateDBConnection() (*sql.DB, error) {

	// 构建数据库连接字符串
	dbConnectionString := "ktv_user:ZAQ!2wsx@tcp(rm-7xvw6sojr7n6781uaxo.mysql.rds.aliyuncs.com:3306)/ktv-basic?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai"
	//dbConnectionString := "root:123456@tcp(127.0.0.1:3306)/music?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai"

	// 打开数据库连接
	db, err := sql.Open("mysql", dbConnectionString)
	if err != nil {
		return nil, err
	}

	// 测试连接
	err = db.<PERSON>()
	if err != nil {
		return nil, err
	}

	return db, nil
}

func main() {
	// 选择要执行的功能
	fmt.Println("请选择要执行的功能：")
	fmt.Println("1. 从文件统计歌曲播放量")
	fmt.Println("2. 清理数据库重复记录")
	fmt.Println("3. 根据vid数组和日期查询播放量")
	fmt.Println("4. 导出指定vid的详细记录（1484085, 2025-05-17~2025-05-18）")
	fmt.Println("5. 导出 sceneType 为 1,2,5 的 vid")
	fmt.Print("请输入选项 (1, 2, 3, 4 或 5): ")

	var choice int
	_, err := fmt.Scanf("%d", &choice)
	if err != nil {
		fmt.Printf("输入错误: %v\n", err)
		return
	}

	switch choice {
	case 1:
		// 从文件统计歌曲播放量
		songCodes := []int64{6625526682240150,
			6654550181453460,
			6315145763604900,
			6654550229033190,
			6625526695123630,
			6387856989459740,
			6315145531735290,
			6625526739778360,
			6388462585468140,
			6843908891163350,
			6625526797509980,
			6654550212463820,
			6654550106832910,
			6625526871745940,
			6654550128826440,
			6654550120438200,
			6625526747194040,
			6625526945047550,
			6654550108375580,
			6625526989292290,
			6654550229582030,
			6654550170429460,
			6625526854867110,
			6625526879145010,
			6625526811311330,
			6625526676697570,
			6315145643972570,
			6625526651824460,
			6625526680986690,
			6388424384610470,
			6315145734036540,
			6625526703695850,
			6315145610459510,
			6625526771252030,
			6315145692146230,
			6654550212577040,
			6625526945271940,
			6388469425377430,
			6625526660721950,
			6315145546971630,
			6625526775817960,
			6654550130924080,
			6625526641218430,
			6625526719386250,
			6625526638168960,
			6625526765548690,
			6388416069358340,
			6315145554122440,
			6625526648214030,
			6625526656717500,
			6625526810839550,
			6388464828201640,
			6625526805496240,
			6625526644683410,
			6625526799528570,
			6625526667993550,
			6625526739925210,
			6654550108437270,
			6625526752806480,
			6654550181446950,
			6625526757415700,
			6654550228443260,
			6625526619484580,
			6315145525028630,
			6625526610655890,
			6387813379652630,
			6315145500685550,
			6625526685722560,
			6315145610252520,
			6625526638566120,
			6654550228539200,
			6625526610034330,
			6625526620457440,
			6387830145065530,
			6625526612665740,
			6387827590815060,
			6654550148104360,
			6654550123335160,
			6654550128961820,
			6625526716623900,
			6654550133161490,
			6625526644264230,
			6625526646758230,
			6387819464386020,
			6625526648840320,
			6625526715899220,
			6387833867799670,
			6246262727325690,
			6625526699204620,
			6625526901987680,
			6625526725785270,
			6625526901991500,
			6625526699117260,
			6388415954350510,
			6625526625669790,
			6315145716182660,
			6625526743869780,
			6625526901936300,
			6654550267032730,
			6625526603579120,
			6246262727285680,
			6625526806728900,
			6654550266740830,
			6625526605875460,
			6246262727304010,
			6654550225935700,
			6625526811968440,
			6625526755317260,
			6654550225325120,
			6625526795114120,
			6625526982770580,
			6625526811383940,
			6625526652938770,
			6315145546967260,
			6625526689633920,
			6625526916464030,
			6625526640700260,
			6315145509762020,
			6654550223178030,
			6625526700816370,
			6625526649579560,
			6625526668625960,
			6625526760170160,
			6315145671778900,
			6654550211983460,
			6654550213596920,
			6625526725789240,
			6625526901983420,
			6654550153422290,
			6625526985308730,
			6625526731342650,
			6625526810726580,
			6315145515555740,
			6625527011244580,
			6625526638224960,
			6315145574627490,
			6625526874193840,
			6654550175763890,
			6315145610106370,
			6625526795319520,
			6625526766007010,
			6625526901972370,
			6625526733804080,
			6625526683529270,
			6315145827653010,
			6315145771574570,
			6625526754755230,
			6654550269431800,
			6387918747892210,
			6246262727321390,
			6654550128824020,
			6625526945268710,
			6625526783191730,
			6387875040172960,
			6625526864372690,
			6315145694495740,
			6625526608351780,
			6387830963237720,
			6246262727322921,
			6625526625018040,
			6625526608909220,
			6625526603797270,
			6387805158554710,
			6246262727304491,
			6625526605404050,
			6625526989101750,
			6625526794361360,
			6654550226827970,
			6625526796660940,
			6625526634676370,
			6246262727324200,
			6654550175766390,
			6388089959531600,
			6315145705913450,
			6625526613533040,
			6387805946161410,
			6315145553058580,
			6625527018185600,
			6625526758194740,
			6625526939207750,
			6625526702886340,
			6388461285255300,
			6315145693232520,
			6654550227251210,
			6625526716407220,
			6625526688755140,
			6625526716187980,
			6654550111321840,
			6625526901768740,
			6625526863294430,
			6625526615806550,
			6654550130775130,
			6625526844544990,
			6654550227554200,
			6625526904653140,
			6375715356697110,
			6625526770401430,
			6654550127581880,
			6625526619672450,
			6654550218332660,
			6625526610548820,
			6654550106836600,
			6625526731619110,
			6625526656855990,
			6387825203879320,
			6315145542270830,
			6625526682041540,
			6315145546961510,
			6654550175673540,
			6315145681567740}

		// 将 int64 数组转换为字符串数组
		songCodeStrings := make([]string, len(songCodes))
		for i, code := range songCodes {
			songCodeStrings[i] = fmt.Sprintf("%d", code)
		}

		// 调用从文件读取的函数
		err := ExportSongCodePlayNumFromFileEnter()
		if err != nil {
			fmt.Printf("Error exporting song code play num from files: %v\n", err)
			return
		}

	case 2:
		// 清理数据库重复记录
		err := CleanDuplicateRecordsEnter()
		if err != nil {
			fmt.Printf("Error cleaning duplicate records: %v\n", err)
			return
		}

	case 3:
		// 根据vid数组和日期查询播放量
		err := ExportVidPlayNumByDateEnter()
		if err != nil {
			fmt.Printf("Error querying vid play numbers by date: %v\n", err)
			return
		}

	case 4:
		// 导出指定vid的详细记录
		err := ExportSpecificVidDetailRecords()
		if err != nil {
			fmt.Printf("Error exporting specific vid detail records: %v\n", err)
			return
		}

	default:
		fmt.Println("无效的选项，请输入 1, 2, 3 或 4")
		return
	}
}
