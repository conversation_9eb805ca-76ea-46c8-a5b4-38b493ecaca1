package main

import (
	"database/sql"
	"fmt"
	_ "github.com/go-sql-driver/mysql"
)

// 生成一个 数据库连接
func CreateDBConnection() (*sql.DB, error) {

	// 构建数据库连接字符串
	dbConnectionString := "ktv_user:ZAQ!2wsx@tcp(rm-7xvw6sojr7n6781uaxo.mysql.rds.aliyuncs.com:3306)/ktv-basic?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai"
	//dbConnectionString := "root:123456@tcp(127.0.0.1:3306)/music?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai"

	// 打开数据库连接
	db, err := sql.Open("mysql", dbConnectionString)
	if err != nil {
		return nil, err
	}

	// 测试连接
	err = db.<PERSON>()
	if err != nil {
		return nil, err
	}

	return db, nil
}

func main() {
	// 选择要执行的功能
	fmt.Println("请选择要执行的功能：")
	fmt.Println("1. 从文件统计歌曲播放量")
	fmt.Println("2. 清理数据库重复记录")
	fmt.Println("3. 根据vid数组和日期查询播放量")
	fmt.Println("4. 导出指定vid的详细记录（1484085, 2025-05-17~2025-05-18）")
	fmt.Println("5. 导出 sceneType 为 1,2,5 的 vid")
	fmt.Println("6. 导出5月份 PlayType=3 按songDuration分组的调用总量")
	fmt.Println("7. 整理输出文件到分类目录")
	fmt.Print("请输入选项 (1, 2, 3, 4, 5, 6 或 7): ")

	var choice int
	_, err := fmt.Scanf("%d", &choice)
	if err != nil {
		fmt.Printf("输入错误: %v\n", err)
		return
	}

	switch choice {
	case 1:
		// 从文件统计歌曲播放量
		// 调用从文件读取的函数
		err := ExportSongCodePlayNumFromFileEnter()
		if err != nil {
			fmt.Printf("Error exporting song code play num from files: %v\n", err)
			return
		}

	case 2:
		// 清理数据库重复记录
		err := CleanDuplicateRecordsEnter()
		if err != nil {
			fmt.Printf("Error cleaning duplicate records: %v\n", err)
			return
		}

	case 3:
		// 根据vid数组和日期查询播放量
		err := ExportVidPlayNumByDateEnter()
		if err != nil {
			fmt.Printf("Error querying vid play numbers by date: %v\n", err)
			return
		}

	case 4:
		// 导出指定vid的详细记录
		err := ExportSpecificVidDetailRecords()
		if err != nil {
			fmt.Printf("Error exporting specific vid detail records: %v\n", err)
			return
		}

	case 5:
		// 导出 sceneType 为 1,2,5 的 vid
		err := ExportVidsBySceneTypeEnter()
		if err != nil {
			fmt.Printf("Error exporting vids by scene type: %v\n", err)
			return
		}

	case 6:
		// 导出5月份 PlayType=3 按songDuration分组的调用总量
		err := ExportPlayType3BySongDurationEnter()
		if err != nil {
			fmt.Printf("Error exporting PlayType 3 by song duration: %v\n", err)
			return
		}

	case 7:
		// 整理输出文件到分类目录
		err := OrganizeOutputFilesEnter()
		if err != nil {
			fmt.Printf("Error organizing output files: %v\n", err)
			return
		}

	default:
		fmt.Println("无效的选项，请输入 1, 2, 3, 4, 5, 6 或 7")
		return
	}
}
