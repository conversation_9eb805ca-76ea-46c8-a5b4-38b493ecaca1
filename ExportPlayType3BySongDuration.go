package main

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"path/filepath"
	"time"
)

// PlayType3Record 表示PlayType=3的记录
type PlayType3Record struct {
	ID           int64
	Lts          int64
	LtsUTC       string
	Vid          string
	UniqueId     string
	CpId         string
	SongCode     string
	SongDuration int64
	PlayDuration int64
	StartTime    int64
	StartTimeUTC string
	EndTime      int64
	EndTimeUTC   string
	PlaybackPos  int64
	PlayNum      int64
	SceneType    int
	PlayType     int
	FreeType     int
	RecordType   int
	UseType      int
}

// SongDurationGroup 表示按songDuration分组的统计结果
type SongDurationGroup struct {
	DurationRange string // "≤50秒" 或 ">50秒"
	TotalPlayNum  int64
	RecordCount   int
}

// SongDurationSummary 表示按songDuration分组的汇总统计
type SongDurationSummary struct {
	DurationRange string
	TotalPlayNum  int64
	RecordCount   int
}

// VidSongDurationSummary 表示按vid和songDuration分组的详细统计
type VidSongDurationSummary struct {
	Vid           string
	DurationRange string
	TotalPlayNum  int64
	RecordCount   int
}

// GetPlayType3SummaryForMay 获取5月份PlayType=3按songDuration分组的统计汇总
func GetPlayType3SummaryForMay() (map[string]*SongDurationSummary, error) {
	// 获取数据库连接
	db, err := CreateOptimizedDBConnection()
	if err != nil {
		return nil, fmt.Errorf("error creating database connection: %w", err)
	}
	defer db.Close()

	// 注意：如果是2025年4月，需要调整时间戳
	//var startTimestamp, endTimestamp int64 = 1746057600, 1746057600

	// 4月份的时间戳范围 (2025-04-01 00:00:00 到 2025-04-30 23:59:59)
	startTimestamp := int64(1743465600) // 2025-04-01 00:00:00 UTC
	endTimestamp := int64(1746057599)   // 2025-04-30 23:59:59 UTC

	// 北京时间 4月份的时间戳范围 (2025-04-01 00:00:00 到 2025-04-30 23:59:59)
	//startTimestamp := int64(1743436800) // 2025-04-01 00:00:00 bj
	//endTimestamp := int64(1746028799)   // 2025-04-30 23:59:59 bj

	// 5月份的时间戳范围 (2025-05-01 00:00:00 到 2025-05-31 23:59:59)
	//startTimestamp := int64(1714521600) // 2024-05-01 00:00:00 UTC
	//endTimestamp := int64(1717199999)   // 2024-05-31 23:59:59 UTC

	// 注意：如果是2025年5月，需要调整时间戳
	// 2025-05-01 00:00:00 UTC = 1746057600
	// 2025-05-31 23:59:59 UTC = 1748735999
	//startTimestamp = 1746057600 // 2025-05-01 00:00:00 UTC
	//endTimestamp = 1748735999  // 2025-05-31 23:59:59 UTC

	query := `
		SELECT
			CASE
				WHEN song_duration <= 50 THEN '≤50秒'
				ELSE '>50秒'
			END as duration_range,
			SUM(play_num) as total_play_num,
			COUNT(*) as record_count
		FROM song_play_record
		WHERE play_type = 3
		AND start_time >= ? AND start_time <= ?
		GROUP BY
			CASE
				WHEN song_duration <= 50 THEN '≤50秒'
				ELSE '>50秒'
			END
		ORDER BY duration_range
	`

	fmt.Printf("查询5月份PlayType=3的记录...\n")
	fmt.Printf("时间范围: %s 到 %s\n",
		time.Unix(startTimestamp, 0).UTC().Format("2006-01-02 15:04:05"),
		time.Unix(endTimestamp, 0).UTC().Format("2006-01-02 15:04:05"))
	fmt.Printf("Query: %s\n", query)

	// 执行查询
	rows, err := db.Query(query, startTimestamp, endTimestamp)
	if err != nil {
		return nil, fmt.Errorf("error executing query: %w", err)
	}
	defer rows.Close()

	// 解析结果
	results := make(map[string]*SongDurationSummary)

	for rows.Next() {
		var durationRange string
		var totalPlayNum int64
		var recordCount int

		err := rows.Scan(&durationRange, &totalPlayNum, &recordCount)
		if err != nil {
			return nil, fmt.Errorf("error scanning row: %w", err)
		}

		results[durationRange] = &SongDurationSummary{
			DurationRange: durationRange,
			TotalPlayNum:  totalPlayNum,
			RecordCount:   recordCount,
		}
	}

	return results, nil
}

// GetPlayType3VidSummaryForMay 获取5月份PlayType=3按vid和songDuration分组的详细统计
func GetPlayType3VidSummaryForMay() ([]VidSongDurationSummary, error) {
	// 获取数据库连接
	db, err := CreateOptimizedDBConnection()
	if err != nil {
		return nil, fmt.Errorf("error creating database connection: %w", err)
	}
	defer db.Close()

	// 5月份的时间戳范围   utc
	//startTimestamp := int64(1746057600) // 2025-05-01 00:00:00 UTC
	//endTimestamp := int64(1748735999)   // 2025-05-31 23:59:59 UTC

	// 5月份的时间戳范围 北京时间
	//startTimestamp := int64(1746028800) // 2025-05-01 00:00:00 UTC
	//endTimestamp := int64(1748707199)   // 2025-05-31 23:59:59 UTC

	// 4月份的时间戳范围 (2025-04-01 00:00:00 到 2025-04-30 23:59:59)
	//startTimestamp := int64(1743465600) // 2025-04-01 00:00:00 UTC
	//endTimestamp := int64(1746057599)   // 2025-04-30 23:59:59 UTC

	// 北京时间 4月份的时间戳范围 (2025-04-01 00:00:00 到 2025-04-30 23:59:59)
	startTimestamp := int64(1743436800) // 2025-04-01 00:00:00 bj
	endTimestamp := int64(1746028799)   // 2025-04-30 23:59:59 bj

	query := `
		SELECT
			vid,
			CASE
				WHEN song_duration <= 50 THEN '≤50秒'
				ELSE '>50秒'
			END as duration_range,
			SUM(play_num) as total_play_num,
			COUNT(*) as record_count
		FROM song_play_record
		WHERE play_type = 3
		AND start_time >= ? AND start_time <= ?
		GROUP BY
			vid,
			CASE
				WHEN song_duration <= 50 THEN '≤50秒'
				ELSE '>50秒'
			END
		ORDER BY vid, duration_range
	`

	fmt.Printf("查询5月份PlayType=3按vid和songDuration分组的详细统计...\n")
	fmt.Printf("时间范围: %s 到 %s\n",
		time.Unix(startTimestamp, 0).UTC().Format("2006-01-02 15:04:05"),
		time.Unix(endTimestamp, 0).UTC().Format("2006-01-02 15:04:05"))
	fmt.Printf("Query: %s\n", query)

	// 执行查询
	rows, err := db.Query(query, startTimestamp, endTimestamp)
	if err != nil {
		return nil, fmt.Errorf("error executing query: %w", err)
	}
	defer rows.Close()

	// 解析结果
	var results []VidSongDurationSummary

	for rows.Next() {
		var vid, durationRange string
		var totalPlayNum int64
		var recordCount int

		err := rows.Scan(&vid, &durationRange, &totalPlayNum, &recordCount)
		if err != nil {
			return nil, fmt.Errorf("error scanning row: %w", err)
		}

		results = append(results, VidSongDurationSummary{
			Vid:           vid,
			DurationRange: durationRange,
			TotalPlayNum:  totalPlayNum,
			RecordCount:   recordCount,
		})
	}

	return results, nil
}

// ExportSummaryToExcel 将汇总统计导出到Excel
func ExportSummaryToExcel(summaries map[string]*SongDurationSummary, outputPath string) error {
	// 创建新的Excel文件
	f := excelize.NewFile()

	// 获取默认工作表名称
	sheetName := f.GetSheetName(0)

	// 定义表头
	headers := []string{"duration_range", "total_play_num", "record_count"}

	// 写入表头
	for colIdx, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(colIdx+1, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// 写入数据
	row := 2
	for _, summary := range summaries {
		// duration_range
		cell, _ := excelize.CoordinatesToCellName(1, row)
		f.SetCellValue(sheetName, cell, summary.DurationRange)

		// total_play_num
		cell, _ = excelize.CoordinatesToCellName(2, row)
		f.SetCellValue(sheetName, cell, summary.TotalPlayNum)

		// record_count
		cell, _ = excelize.CoordinatesToCellName(3, row)
		f.SetCellValue(sheetName, cell, summary.RecordCount)

		row++
	}

	// 设置列宽以提高可读性
	for i := 0; i < len(headers); i++ {
		colName, _ := excelize.ColumnNumberToName(i + 1)
		f.SetColWidth(sheetName, colName, colName, 20)
	}

	// 保存文件
	err := f.SaveAs(outputPath)
	if err != nil {
		return fmt.Errorf("error saving Excel file: %w", err)
	}

	return nil
}

// ExportVidDetailSummaryToExcel 将按vid分组的详细统计导出到Excel
func ExportVidDetailSummaryToExcel(vidSummaries []VidSongDurationSummary, outputPath string) error {
	// 创建新的Excel文件
	f := excelize.NewFile()

	// 获取默认工作表名称
	sheetName := f.GetSheetName(0)

	// 定义表头
	headers := []string{"vid", "duration_range", "total_play_num", "record_count"}

	// 写入表头
	for colIdx, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(colIdx+1, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// 写入数据
	for rowIdx, summary := range vidSummaries {
		row := rowIdx + 2 // 从第2行开始写入数据（第1行是表头）

		// vid
		cell, _ := excelize.CoordinatesToCellName(1, row)
		f.SetCellValue(sheetName, cell, summary.Vid)

		// duration_range
		cell, _ = excelize.CoordinatesToCellName(2, row)
		f.SetCellValue(sheetName, cell, summary.DurationRange)

		// total_play_num
		cell, _ = excelize.CoordinatesToCellName(3, row)
		f.SetCellValue(sheetName, cell, summary.TotalPlayNum)

		// record_count
		cell, _ = excelize.CoordinatesToCellName(4, row)
		f.SetCellValue(sheetName, cell, summary.RecordCount)
	}

	// 设置列宽以提高可读性
	for i := 0; i < len(headers); i++ {
		colName, _ := excelize.ColumnNumberToName(i + 1)
		f.SetColWidth(sheetName, colName, colName, 20)
	}

	// 保存文件
	err := f.SaveAs(outputPath)
	if err != nil {
		return fmt.Errorf("error saving Excel file: %w", err)
	}

	return nil
}

// ExportVidDetailSummaryByDurationRange 按songDuration分组导出vid详细统计
func ExportVidDetailSummaryByDurationRange(vidSummaries []VidSongDurationSummary, outputDir, timestamp string) error {
	// 按duration_range分组
	groupedSummaries := make(map[string][]VidSongDurationSummary)

	for _, summary := range vidSummaries {
		groupedSummaries[summary.DurationRange] = append(groupedSummaries[summary.DurationRange], summary)
	}

	// 为每个分组导出单独的文件
	for durationRange, summaries := range groupedSummaries {
		var fileName string
		if durationRange == "≤50秒" {
			fileName = fmt.Sprintf("PlayType3_May_VidDetail_LTE50s_%s.xlsx", timestamp)
		} else {
			fileName = fmt.Sprintf("PlayType3_May_VidDetail_GT50s_%s.xlsx", timestamp)
		}

		outputPath := filepath.Join(outputDir, fileName)

		fmt.Printf("导出%s的vid详细统计到: %s\n", durationRange, outputPath)
		err := ExportVidDetailSummaryToExcel(summaries, outputPath)
		if err != nil {
			return fmt.Errorf("error exporting %s vid detail summary: %w", durationRange, err)
		}

		fmt.Printf("%s的vid详细统计导出完成: %d 个vid\n", durationRange, len(summaries))
	}

	return nil
}

// ExportPlayType3BySongDurationEnter 主入口函数
func ExportPlayType3BySongDurationEnter() error {
	fmt.Println("开始导出5月份 PlayType=3 按songDuration分组的调用总量...")

	// 1. 查询5月份PlayType=3的统计汇总
	fmt.Println("正在查询5月份PlayType=3的统计汇总...")
	summaries, err := GetPlayType3SummaryForMay()
	if err != nil {
		return fmt.Errorf("error getting PlayType 3 summary for May: %w", err)
	}

	fmt.Printf("查询完成，共找到 %d 个分组\n", len(summaries))

	if len(summaries) == 0 {
		fmt.Println("没有找到符合条件的记录")
		return nil
	}

	// 2. 创建输出目录
	baseOutputDir := "output"
	outputDir := filepath.Join(baseOutputDir, "PlayType3_Analysis")
	err = createOutputDir(baseOutputDir)
	if err != nil {
		return fmt.Errorf("error creating base output directory: %w", err)
	}
	err = createOutputDir(outputDir)
	if err != nil {
		return fmt.Errorf("error creating PlayType3 output directory: %w", err)
	}

	// 3. 生成时间戳用于文件名
	timestamp := time.Now().Format("20060102_150405")

	// 4. 导出汇总统计文件
	summaryFileName := fmt.Sprintf("PlayType3_May_SongDuration_Summary_%s.xlsx", timestamp)
	summaryOutputPath := filepath.Join(outputDir, summaryFileName)

	fmt.Printf("导出汇总统计到: %s\n", summaryOutputPath)
	err = ExportSummaryToExcel(summaries, summaryOutputPath)
	if err != nil {
		return fmt.Errorf("error exporting summary: %w", err)
	}

	// 5. 为每个分组单独导出文件
	for _, summary := range summaries {
		var fileName string
		if summary.DurationRange == "≤50秒" {
			fileName = fmt.Sprintf("PlayType3_May_SongDuration_LTE50s_Summary_%s.xlsx", timestamp)
		} else {
			fileName = fmt.Sprintf("PlayType3_May_SongDuration_GT50s_Summary_%s.xlsx", timestamp)
		}

		outputPath := filepath.Join(outputDir, fileName)

		// 创建单个分组的map
		singleSummary := map[string]*SongDurationSummary{
			summary.DurationRange: summary,
		}

		fmt.Printf("导出分组 %s 到: %s\n", summary.DurationRange, outputPath)
		err = ExportSummaryToExcel(singleSummary, outputPath)
		if err != nil {
			return fmt.Errorf("error exporting %s summary: %w", summary.DurationRange, err)
		}
	}

	// 6. 打印统计信息
	fmt.Println("\n=== 统计信息 ===")

	totalRecords := 0
	totalPlayNum := int64(0)
	for _, summary := range summaries {
		fmt.Printf("分组 %s: %d 条记录, 调用总量: %d\n",
			summary.DurationRange, summary.RecordCount, summary.TotalPlayNum)
		totalRecords += summary.RecordCount
		totalPlayNum += summary.TotalPlayNum
	}
	fmt.Printf("总记录数: %d\n", totalRecords)
	fmt.Printf("总调用量: %d\n", totalPlayNum)

	// 7. 查询并导出按vid分组的详细统计
	fmt.Println("\n正在查询按vid分组的详细统计...")
	vidSummaries, err := GetPlayType3VidSummaryForMay()
	if err != nil {
		return fmt.Errorf("error getting vid summary for May: %w", err)
	}

	fmt.Printf("vid详细统计查询完成，共找到 %d 条记录\n", len(vidSummaries))

	if len(vidSummaries) > 0 {
		// 导出所有vid的详细统计
		allVidFileName := fmt.Sprintf("PlayType3_May_VidDetail_All_%s.xlsx", timestamp)
		allVidOutputPath := filepath.Join(outputDir, allVidFileName)

		fmt.Printf("导出所有vid详细统计到: %s\n", allVidOutputPath)
		err = ExportVidDetailSummaryToExcel(vidSummaries, allVidOutputPath)
		if err != nil {
			return fmt.Errorf("error exporting all vid detail summary: %w", err)
		}

		// 按songDuration分组导出vid详细统计
		err = ExportVidDetailSummaryByDurationRange(vidSummaries, outputDir, timestamp)
		if err != nil {
			return fmt.Errorf("error exporting vid detail summary by duration range: %w", err)
		}

		// 打印vid统计信息
		fmt.Println("\n=== Vid详细统计信息 ===")
		vidStats := make(map[string]int)
		vidPlayNumStats := make(map[string]int64)

		for _, vidSummary := range vidSummaries {
			vidStats[vidSummary.DurationRange]++
			vidPlayNumStats[vidSummary.DurationRange] += vidSummary.TotalPlayNum
		}

		for durationRange, count := range vidStats {
			fmt.Printf("分组 %s: %d 个vid, 调用总量: %d\n",
				durationRange, count, vidPlayNumStats[durationRange])
		}
	}

	fmt.Println("\n导出完成！")
	return nil
}
