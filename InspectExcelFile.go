package main

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"strings"
)

// InspectExcelFile 检查Excel文件的结构
func InspectExcelFile(filePath string) error {
	fmt.Printf("正在检查文件: %s\n", filePath)
	
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return fmt.Errorf("error opening file: %w", err)
	}
	defer f.Close()

	// 获取所有工作表名称
	sheetNames := f.GetSheetList()
	fmt.Printf("工作表数量: %d\n", len(sheetNames))
	fmt.Printf("工作表名称: %v\n", sheetNames)

	// 检查每个工作表
	for i, sheetName := range sheetNames {
		fmt.Printf("\n=== 工作表 %d: %s ===\n", i+1, sheetName)
		
		rows, err := f.GetRows(sheetName)
		if err != nil {
			fmt.Printf("读取工作表失败: %v\n", err)
			continue
		}

		fmt.Printf("总行数: %d\n", len(rows))
		
		// 显示前10行的内容
		maxRows := 10
		if len(rows) < maxRows {
			maxRows = len(rows)
		}

		for j := 0; j < maxRows; j++ {
			row := rows[j]
			fmt.Printf("第%d行 (%d列): ", j+1, len(row))
			
			// 显示每列的内容，限制长度
			for k, cell := range row {
				cellStr := fmt.Sprintf("%v", cell)
				if len(cellStr) > 30 {
					cellStr = cellStr[:30] + "..."
				}
				fmt.Printf("[%d]%s ", k, cellStr)
			}
			fmt.Println()
		}

		if len(rows) > maxRows {
			fmt.Printf("... (还有 %d 行)\n", len(rows)-maxRows)
		}
	}

	return nil
}

// InspectBothFiles 检查两个文件的结构
func InspectBothFiles() error {
	fmt.Println("开始检查文件结构...")

	// 检查歌曲清单文件
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("检查唱鸭案涉歌曲清单文件")
	fmt.Println(strings.Repeat("=", 60))
	err := InspectExcelFile("output/唱鸭案涉歌曲清单（6143号案）.xlsx")
	if err != nil {
		fmt.Printf("检查歌曲清单文件失败: %v\n", err)
	}

	// 检查播放量文件
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("检查播放量文件")
	fmt.Println(strings.Repeat("=", 60))
	err = InspectExcelFile("output/SongCode_Analysis/songcode_total_playnum_20250617_142501.xlsx")
	if err != nil {
		fmt.Printf("检查播放量文件失败: %v\n", err)
	}

	// 检查匹配后的文件
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("检查匹配后的文件")
	fmt.Println(strings.Repeat("=", 60))
	err = InspectExcelFile("output/唱鸭案涉歌曲清单_已匹配播放量_20250617_161213.xlsx")
	if err != nil {
		fmt.Printf("检查匹配后文件失败: %v\n", err)
	}

	return nil
}
