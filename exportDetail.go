package main

import (
	"encoding/csv"
	"fmt"
	"github.com/xuri/excelize/v2"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

// KtvUsageRecord represents a single record from the KTV usage CSV files
type KtvUsageRecord struct {
	Lts          string
	Vid          string
	UniqueId     string
	CpId         string
	SongCode     string
	SongDuration string
	PlayDuration string
	StartTime    string
	EndTime      string
	PlaybackPos  string
	PlayNum      string
	SceneType    string
	PlayType     string
	FreeType     string
	RecordType   string
	UseType      string
}

// GetStatisticsByVids retrieves statistics for the specified vid array from the target folder
func GetStatisticsByVids(vids []string) ([]KtvUsageRecord, error) {
	// Define the target directory containing the CSV files
	targetDir := "202504解压"

	// Get all CSV files in the target directory
	files, err := filepath.Glob(filepath.Join(targetDir, "*.csv"))
	if err != nil {
		return nil, fmt.Errorf("error finding CSV files: %w", err)
	}

	// Create a map for faster lookup of vids
	vidMap := make(map[string]bool)
	for _, vid := range vids {
		vidMap[vid] = true
	}

	// Store the filtered results
	var results []KtvUsageRecord

	// Process each CSV file
	for _, file := range files {
		// Open the file
		f, err := os.Open(file)
		if err != nil {
			return nil, fmt.Errorf("error opening file %s: %w", file, err)
		}
		defer f.Close()

		// Create a CSV reader
		reader := csv.NewReader(f)

		// Read the header
		header, err := reader.Read()
		if err != nil {
			return nil, fmt.Errorf("error reading header from %s: %w", file, err)
		}

		// Find the index of the vid column
		vidIndex := -1
		for i, col := range header {
			if strings.ToLower(col) == "vid" {
				vidIndex = i
				break
			}
		}

		if vidIndex == -1 {
			return nil, fmt.Errorf("vid column not found in %s", file)
		}

		// Read and filter the records
		for {
			record, err := reader.Read()
			if err == io.EOF {
				break
			}
			if err != nil {
				return nil, fmt.Errorf("error reading record from %s: %w", file, err)
			}

			// Check if the vid is in our target list
			if vidIndex < len(record) && vidMap[record[vidIndex]] {
				// Create a KtvUsageRecord from the CSV record
				usageRecord := KtvUsageRecord{}

				// Assign values based on the header order
				for i, col := range header {
					if i < len(record) {
						switch strings.ToLower(col) {
						case "lts":
							usageRecord.Lts = record[i]
						case "vid":
							usageRecord.Vid = record[i]
						case "uniqueid":
							usageRecord.UniqueId = record[i]
						case "cpid":
							usageRecord.CpId = record[i]
						case "songcode":
							usageRecord.SongCode = record[i]
						case "songduration":
							usageRecord.SongDuration = record[i]
						case "playduration":
							usageRecord.PlayDuration = record[i]
						case "starttime":
							usageRecord.StartTime = record[i]
						case "endtime":
							usageRecord.EndTime = record[i]
						case "playbackpos":
							usageRecord.PlaybackPos = record[i]
						case "playnum":
							usageRecord.PlayNum = record[i]
						case "scenetype":
							usageRecord.SceneType = record[i]
						case "playtype":
							usageRecord.PlayType = record[i]
						case "freetype":
							usageRecord.FreeType = record[i]
						case "recordtype":
							usageRecord.RecordType = record[i]
						case "usetype":
							usageRecord.UseType = record[i]
						}
					}
				}

				// Add the record to our results
				results = append(results, usageRecord)
			}
		}
	}

	return results, nil
}

// ConvertMillisToUTCTimeString converts UTC milliseconds timestamp to a readable UTC time string
func ConvertMillisToUTCTimeString(millisStr string) string {
	// Parse the milliseconds timestamp
	millis, err := strconv.ParseInt(millisStr, 10, 64)
	if err != nil {
		return millisStr // Return original string if parsing fails
	}

	// Check if this is a Unix timestamp in milliseconds (13 digits typically)
	// or a different format
	var t time.Time
	if millis > 1000000000000 { // Likely a millisecond timestamp from 2001 onwards
		// Convert milliseconds to time.Time (Unix milliseconds)
		t = time.UnixMilli(millis)
	} else {
		// For other formats, treat as seconds
		t = time.Unix(millis, 0)
	}

	// Format the time as a readable string (ISO 8601 format) in UTC
	return t.UTC().Format("2006-01-02 15:04:05.000")
}

// ConvertMillisToUTCTimeString2 converts milliseconds timestamp to a readable UTC time string
func ConvertMillisToUTCTimeString2(millisStr string) string {
	// Parse the milliseconds timestamp
	millis, err := strconv.ParseInt(millisStr, 10, 64)
	if err != nil {
		return millisStr // Return original string if parsing fails
	}

	// Check if this is a Unix timestamp in milliseconds (13 digits typically)
	// or a different format
	var t time.Time
	if millis > 1000000000000 { // Likely a millisecond timestamp from 2001 onwards
		// Convert milliseconds to time.Time (Unix milliseconds)
		t = time.UnixMilli(millis)
	} else {
		// For other formats, treat as seconds
		t = time.Unix(millis, 0)
	}

	// Convert to UTC time and format
	return t.UTC().Format("2006-01-02 15:04:05.000")
}

// ExportDetailedResultsByVid exports detailed results to separate XLSX files by vid
func ExportDetailedResultsByVid(records []KtvUsageRecord, outputDir string, limit int) error {
	// Group records by vid
	recordsByVid := make(map[string][]KtvUsageRecord)
	for _, record := range records {
		recordsByVid[record.Vid] = append(recordsByVid[record.Vid], record)
	}

	// Generate timestamp for filenames
	timestamp := time.Now().Format("20060102_150405")

	// Define headers
	header := []string{
		"lts", "lts_utc", "lts_utc2", "vid", "uniqueId", "cpId", "songCode", "songDuration",
		"playDuration", "startTime", "startTime_utc", "startTime_utc2", "endTime", "endTime_utc", "endTime_utc2",
		"playbackPos", "playNum", "sceneType", "playType", "freeType", "recordType", "useType",
	}

	// Export records for each vid
	for vid, vidRecords := range recordsByVid {
		// Create a new Excel file
		f := excelize.NewFile()

		// Get the default sheet name
		sheetName := f.GetSheetName(0)

		// Write the header
		for colIdx, colName := range header {
			cell, _ := excelize.CoordinatesToCellName(colIdx+1, 1)
			f.SetCellValue(sheetName, cell, colName)
		}

		// Determine how many records to export
		recordCount := len(vidRecords)
		if limit > 0 && limit < recordCount {
			recordCount = limit
		}

		// Write each record up to the limit
		for i := 0; i < recordCount; i++ {
			record := vidRecords[i]
			rowIdx := i + 2 // +2 because row 1 is the header and Excel is 1-indexed

			// Prepare row data
			rowData := []interface{}{
				record.Lts,
				ConvertMillisToUTCTimeString(record.Lts),
				ConvertMillisToUTCTimeString2(record.Lts),
				//0,
				record.Vid,
				record.UniqueId,
				record.CpId,
				record.SongCode,
				record.SongDuration,
				record.PlayDuration,
				record.StartTime,
				ConvertMillisToUTCTimeString(record.StartTime),
				ConvertMillisToUTCTimeString2(record.StartTime),
				//0,
				record.EndTime,
				ConvertMillisToUTCTimeString(record.EndTime),
				ConvertMillisToUTCTimeString2(record.EndTime),
				//0,
				record.PlaybackPos,
				record.PlayNum,
				record.SceneType,
				record.PlayType,
				record.FreeType,
				record.RecordType,
				record.UseType,
			}

			// Write row data
			for colIdx, cellValue := range rowData {
				cell, _ := excelize.CoordinatesToCellName(colIdx+1, rowIdx)
				f.SetCellValue(sheetName, cell, cellValue)
			}
		}

		// Set column width for better readability
		for i := 0; i < len(header); i++ {
			colName, _ := excelize.ColumnNumberToName(i + 1)
			f.SetColWidth(sheetName, colName, colName, 15)
		}

		// Create the output file path
		outputPath := filepath.Join(outputDir, fmt.Sprintf("detailed_results_vid_%s_%s.xlsx", vid, timestamp))

		// Save the Excel file
		if err := f.SaveAs(outputPath); err != nil {
			return fmt.Errorf("error saving Excel file for vid %s: %w", vid, err)
		}

		// Close the Excel file
		if err := f.Close(); err != nil {
			return fmt.Errorf("error closing Excel file for vid %s: %w", vid, err)
		}

		fmt.Printf("Exported %d records for VID %s to %s\n", recordCount, vid, outputPath)
	}

	return nil
}

// ExportSummaryResults exports the summary results to an XLSX file
func ExportSummaryResults(summary map[string]map[string]int, outputPath string) error {
	// Create a new Excel file
	f := excelize.NewFile()

	// Get the default sheet name
	sheetName := f.GetSheetName(0)

	// Define headers
	header := []string{"vid", "total_plays", "total_duration", "total_play_num"}

	// Write the header
	for colIdx, colName := range header {
		cell, _ := excelize.CoordinatesToCellName(colIdx+1, 1)
		f.SetCellValue(sheetName, cell, colName)
	}

	// Write each summary record
	rowIdx := 2 // Start from row 2 (after header)
	for vid, stats := range summary {
		// Write vid
		cell, _ := excelize.CoordinatesToCellName(1, rowIdx)
		f.SetCellValue(sheetName, cell, vid)

		// Write total_plays
		cell, _ = excelize.CoordinatesToCellName(2, rowIdx)
		f.SetCellValue(sheetName, cell, stats["total_plays"])

		// Write total_duration
		cell, _ = excelize.CoordinatesToCellName(3, rowIdx)
		f.SetCellValue(sheetName, cell, stats["total_duration"])

		// Write total_play_num
		cell, _ = excelize.CoordinatesToCellName(4, rowIdx)
		f.SetCellValue(sheetName, cell, stats["total_play_num"])

		rowIdx++
	}

	// Set column width for better readability
	for i := 0; i < len(header); i++ {
		colName, _ := excelize.ColumnNumberToName(i + 1)
		f.SetColWidth(sheetName, colName, colName, 15)
	}

	// Change the file extension to .xlsx if it's not already
	if !strings.HasSuffix(outputPath, ".xlsx") {
		outputPath = strings.TrimSuffix(outputPath, filepath.Ext(outputPath)) + ".xlsx"
	}

	// Save the Excel file
	if err := f.SaveAs(outputPath); err != nil {
		return fmt.Errorf("error saving Excel file: %w", err)
	}

	// Close the Excel file
	if err := f.Close(); err != nil {
		return fmt.Errorf("error closing Excel file: %w", err)
	}

	return nil
}

// GetStatisticsSummaryByVids returns a summary of statistics for each vid
func GetStatisticsSummaryByVids(vids []string) (map[string]map[string]int, error) {
	// Get the detailed records
	records, err := GetStatisticsByVids(vids)
	if err != nil {
		return nil, err
	}

	// Create a summary map: vid -> metric -> count
	summary := make(map[string]map[string]int)

	// Initialize the summary map for each vid
	for _, vid := range vids {
		summary[vid] = make(map[string]int)
		summary[vid]["total_plays"] = 0
		summary[vid]["total_duration"] = 0
		summary[vid]["total_play_num"] = 0 // Add total_play_num to track the sum of play_num values
	}

	// Process each record
	for _, record := range records {
		// Increment the play count
		summary[record.Vid]["total_plays"]++

		// Add the play duration if it's a valid number
		if duration, err := strconv.Atoi(record.PlayDuration); err == nil {
			summary[record.Vid]["total_duration"] += duration
		}

		// Add the play_num if it's a valid number
		if playNum, err := strconv.Atoi(record.PlayNum); err == nil {
			summary[record.Vid]["total_play_num"] += playNum
		}
	}

	return summary, nil
}

// ExportDetailedResultsByPlayType exports detailed results to separate XLSX files by playType
func ExportDetailedResultsByPlayType(records []KtvUsageRecord, outputDir string, limit int) error {
	// Group records by playType
	recordsByPlayType := make(map[string][]KtvUsageRecord)
	for _, record := range records {
		recordsByPlayType[record.PlayType] = append(recordsByPlayType[record.PlayType], record)
	}

	// Generate timestamp for filenames
	timestamp := time.Now().Format("20060102_150405")

	// Define headers
	header := []string{
		"lts", "lts_utc", "lts_utc2", "vid", "uniqueId", "cpId", "songCode", "songDuration",
		"playDuration", "startTime", "startTime_utc", "startTime_utc2", "endTime", "endTime_utc", "endTime_utc2",
		"playbackPos", "playNum", "sceneType", "playType", "freeType", "recordType", "useType",
	}

	// Export records for each playType
	for playType, playTypeRecords := range recordsByPlayType {
		// Create a new Excel file
		f := excelize.NewFile()

		// Get the default sheet name
		sheetName := f.GetSheetName(0)

		// Write the header
		for colIdx, colName := range header {
			cell, _ := excelize.CoordinatesToCellName(colIdx+1, 1)
			f.SetCellValue(sheetName, cell, colName)
		}

		// Determine how many records to export
		recordCount := len(playTypeRecords)
		if limit > 0 && limit < recordCount {
			recordCount = limit
		}

		// Write each record up to the limit
		for i := 0; i < recordCount; i++ {
			record := playTypeRecords[i]
			rowIdx := i + 2 // +2 because row 1 is the header and Excel is 1-indexed

			// Prepare row data
			rowData := []interface{}{
				record.Lts,
				ConvertMillisToUTCTimeString(record.Lts),
				ConvertMillisToUTCTimeString2(record.Lts),
				record.Vid,
				record.UniqueId,
				record.CpId,
				record.SongCode,
				record.SongDuration,
				record.PlayDuration,
				record.StartTime,
				ConvertMillisToUTCTimeString(record.StartTime),
				ConvertMillisToUTCTimeString2(record.StartTime),
				record.EndTime,
				ConvertMillisToUTCTimeString(record.EndTime),
				ConvertMillisToUTCTimeString2(record.EndTime),
				record.PlaybackPos,
				record.PlayNum,
				record.SceneType,
				record.PlayType,
				record.FreeType,
				record.RecordType,
				record.UseType,
			}

			// Write row data
			for colIdx, cellValue := range rowData {
				cell, _ := excelize.CoordinatesToCellName(colIdx+1, rowIdx)
				f.SetCellValue(sheetName, cell, cellValue)
			}
		}

		// Set column width for better readability
		for i := 0; i < len(header); i++ {
			colName, _ := excelize.ColumnNumberToName(i + 1)
			f.SetColWidth(sheetName, colName, colName, 15)
		}

		// Create the output file path
		outputPath := filepath.Join(outputDir, fmt.Sprintf("detailed_results_playtype_%s_%s.xlsx", playType, timestamp))

		// Save the Excel file
		if err := f.SaveAs(outputPath); err != nil {
			return fmt.Errorf("error saving Excel file for playType %s: %w", playType, err)
		}

		// Close the Excel file
		if err := f.Close(); err != nil {
			return fmt.Errorf("error closing Excel file for playType %s: %w", playType, err)
		}

		fmt.Printf("Exported %d records for playType %s to %s\n", recordCount, playType, outputPath)
	}

	return nil
}

// ExportDetailedResultsByVidAndPlayType exports detailed results to separate XLSX files by vid and playType combination
func ExportDetailedResultsByVidAndPlayType(records []KtvUsageRecord, outputDir string, limit int) error {
	// Group records by vid and playType
	recordsByVidAndPlayType := make(map[string][]KtvUsageRecord)
	for _, record := range records {
		key := fmt.Sprintf("%s_%s", record.Vid, record.PlayType)
		recordsByVidAndPlayType[key] = append(recordsByVidAndPlayType[key], record)
	}

	// Generate timestamp for filenames
	timestamp := time.Now().Format("20060102_150405")

	// Define headers
	header := []string{
		"lts", "lts_utc", "lts_utc2", "vid", "uniqueId", "cpId", "songCode", "songDuration",
		"playDuration", "startTime", "startTime_utc", "startTime_utc2", "endTime", "endTime_utc", "endTime_utc2",
		"playbackPos", "playNum", "sceneType", "playType", "freeType", "recordType", "useType",
	}

	// Export records for each vid and playType combination
	for key, groupRecords := range recordsByVidAndPlayType {
		// Extract vid and playType from the key
		parts := strings.Split(key, "_")
		vid := parts[0]
		playType := "unknown"
		if len(parts) > 1 {
			playType = parts[1]
		}

		// Create a new Excel file
		f := excelize.NewFile()

		// Get the default sheet name
		sheetName := f.GetSheetName(0)

		// Write the header
		for colIdx, colName := range header {
			cell, _ := excelize.CoordinatesToCellName(colIdx+1, 1)
			f.SetCellValue(sheetName, cell, colName)
		}

		// Determine how many records to export
		recordCount := len(groupRecords)
		if limit > 0 && limit < recordCount {
			recordCount = limit
		}

		// Write each record up to the limit
		for i := 0; i < recordCount; i++ {
			record := groupRecords[i]
			rowIdx := i + 2 // +2 because row 1 is the header and Excel is 1-indexed

			// Prepare row data
			rowData := []interface{}{
				record.Lts,
				ConvertMillisToUTCTimeString(record.Lts),
				ConvertMillisToUTCTimeString2(record.Lts),
				record.Vid,
				record.UniqueId,
				record.CpId,
				record.SongCode,
				record.SongDuration,
				record.PlayDuration,
				record.StartTime,
				ConvertMillisToUTCTimeString(record.StartTime),
				ConvertMillisToUTCTimeString2(record.StartTime),
				record.EndTime,
				ConvertMillisToUTCTimeString(record.EndTime),
				ConvertMillisToUTCTimeString2(record.EndTime),
				record.PlaybackPos,
				record.PlayNum,
				record.SceneType,
				record.PlayType,
				record.FreeType,
				record.RecordType,
				record.UseType,
			}

			// Write row data
			for colIdx, cellValue := range rowData {
				cell, _ := excelize.CoordinatesToCellName(colIdx+1, rowIdx)
				f.SetCellValue(sheetName, cell, cellValue)
			}
		}

		// Set column width for better readability
		for i := 0; i < len(header); i++ {
			colName, _ := excelize.ColumnNumberToName(i + 1)
			f.SetColWidth(sheetName, colName, colName, 15)
		}

		// Create the output file path
		outputPath := filepath.Join(outputDir, fmt.Sprintf("detailed_results_vid_%s_playtype_%s_%s.xlsx", vid, playType, timestamp))

		// Save the Excel file
		if err := f.SaveAs(outputPath); err != nil {
			return fmt.Errorf("error saving Excel file for vid %s and playType %s: %w", vid, playType, err)
		}

		// Close the Excel file
		if err := f.Close(); err != nil {
			return fmt.Errorf("error closing Excel file for vid %s and playType %s: %w", vid, playType, err)
		}

		fmt.Printf("Exported %d records for VID %s and playType %s to %s\n", recordCount, vid, playType, outputPath)
	}

	return nil
}

func ExportDetailEnter() {
	// Example usage
	//vids := []string{"1496093", "1483108", "1333855"}
	//vids := []string{"1483108"}
	//vids := []string{"228881", "195", "1484085", "10179"}
	vids := []string{"1533434"}

	// Get statistics for the specified vids
	fmt.Println("Retrieving data for the specified vids...")
	results, err := GetStatisticsByVids(vids)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}

	// Print the number of records found
	fmt.Printf("Found %d records for the specified vids\n", len(results))

	// Get summary statistics
	summary, err := GetStatisticsSummaryByVids(vids)
	if err != nil {
		fmt.Printf("Error getting summary: %v\n", err)
		return
	}

	// Print the summary
	fmt.Println("Summary statistics:")
	for vid, stats := range summary {
		fmt.Printf("VID: %s\n", vid)
		fmt.Printf("  Total plays: %d\n", stats["total_plays"])
		fmt.Printf("  Total duration: %d seconds\n", stats["total_duration"])
		fmt.Printf("  Total play_num: %d\n", stats["total_play_num"])
	}

	// Create output directory if it doesn't exist
	outputDir := "output"
	if _, err := os.Stat(outputDir); os.IsNotExist(err) {
		err = os.Mkdir(outputDir, 0755)
		if err != nil {
			fmt.Printf("Error creating output directory: %v\n", err)
			return
		}
	}

	// Generate timestamp for filenames
	timestamp := time.Now().Format("20060102_150405")

	// Export detailed results by vid
	fmt.Println("Exporting detailed results by vid...")
	err = ExportDetailedResultsByVid(results, outputDir, 1000) // Limit to 1000 records per vid
	if err != nil {
		fmt.Printf("Error exporting detailed results: %v\n", err)
		return
	}

	// Export summary results
	summaryOutputPath := filepath.Join(outputDir, fmt.Sprintf("summary_results_%s.xlsx", timestamp))
	fmt.Printf("Exporting summary results to %s...\n", summaryOutputPath)
	err = ExportSummaryResults(summary, summaryOutputPath)
	if err != nil {
		fmt.Printf("Error exporting summary results: %v\n", err)
		return
	}

	// Export detailed results by playType
	fmt.Println("Exporting detailed results by playType...")
	err = ExportDetailedResultsByPlayType(results, outputDir, 1000) // Limit to 1000 records per playType
	if err != nil {
		fmt.Printf("Error exporting detailed results by playType: %v\n", err)
		return
	}

	// Export detailed results by vid and playType
	fmt.Println("Exporting detailed results by vid and playType...")
	err = ExportDetailedResultsByVidAndPlayType(results, outputDir, 1000) // Limit to 1000 records per vid and playType
	if err != nil {
		fmt.Printf("Error exporting detailed results by vid and playType: %v\n", err)
		return
	}

	fmt.Println("Export completed successfully!")
}
