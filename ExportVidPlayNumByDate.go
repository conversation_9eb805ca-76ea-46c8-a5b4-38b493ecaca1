package main

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// VidPlayNumRecord 表示vid播放量记录
type VidPlayNumRecord struct {
	Vid          string
	Date         string
	PlayType     int
	PlayTypeName string
	TotalPlayNum int64
	RecordCount  int
}

// VidDatePlayNumSummary 表示vid按日期的播放量汇总
type VidDatePlayNumSummary struct {
	Vid           string
	PlayType      int
	PlayTypeName  string
	TotalPlayNum  int64
	DailyPlayNums map[string]*VidPlayNumRecord // 日期 -> 播放量记录
}

// PlayTypeInfo 播放类型信息
type PlayTypeInfo struct {
	PlayType int
	Name     string
	Prefix   string
	IsClip   string
}

// GetPlayTypeInfo 根据play_type获取类型信息
func GetPlayTypeInfo(playType int) PlayTypeInfo {
	switch playType {
	case 1:
		return PlayTypeInfo{PlayType: 1, Name: "DRM", Prefix: "DRM_", IsClip: ""}
	case 2:
		return PlayTypeInfo{PlayType: 2, Name: "明文", Prefix: "明文_", IsClip: ""}
	case 3:
		return PlayTypeInfo{PlayType: 3, Name: "副歌", Prefix: "副歌_", IsClip: "_isClip"}
	default:
		return PlayTypeInfo{PlayType: playType, Name: "未知", Prefix: "未知_", IsClip: ""}
	}
}

// GetPlayNumByVidsAndDateRange 根据vid数组和日期范围从数据库查询播放量（按play_type分组）
func GetPlayNumByVidsAndDateRange(vids []string, startDate, endDate string) (map[string]*VidDatePlayNumSummary, error) {
	// 获取数据库连接
	db, err := CreateOptimizedDBConnection()
	if err != nil {
		return nil, fmt.Errorf("error creating database connection: %w", err)
	}
	defer db.Close()

	// 将日期转换为时间戳范围
	startTimestamp, endTimestamp, err := convertDateRangeToTimestamps(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("error converting date range: %w", err)
	}

	// 构建查询语句
	vidParams := strings.Repeat("?,", len(vids))
	vidParams = vidParams[:len(vidParams)-1] // 移除最后一个逗号

	query := fmt.Sprintf(`
		SELECT
			vid,
			play_type,
			DATE(FROM_UNIXTIME(start_time)) as play_date,
			SUM(play_num) as total_play_num,
			COUNT(*) as record_count
		FROM song_play_record
		WHERE vid IN (%s)
		AND start_time >= ? AND start_time <= ?
		GROUP BY vid, play_type, DATE(FROM_UNIXTIME(start_time))
		ORDER BY vid, play_type, play_date
	`, vidParams)

	// 准备参数
	args := make([]interface{}, 0, len(vids)+2)
	for _, vid := range vids {
		args = append(args, vid)
	}
	args = append(args, startTimestamp, endTimestamp)

	fmt.Printf("Executing query for %d vids from %s to %s\n", len(vids), startDate, endDate)
	fmt.Printf("Query: %s\n", query)

	// 执行查询
	rows, err := db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("error executing query: %w", err)
	}
	defer rows.Close()

	// 解析结果
	results := make(map[string]*VidDatePlayNumSummary)

	for rows.Next() {
		var vid, date string
		var playType int
		var totalPlayNum int64
		var recordCount int

		err := rows.Scan(&vid, &playType, &date, &totalPlayNum, &recordCount)
		if err != nil {
			return nil, fmt.Errorf("error scanning row: %w", err)
		}

		// 获取播放类型信息
		playTypeInfo := GetPlayTypeInfo(playType)

		// 创建唯一键：vid + play_type
		key := fmt.Sprintf("%s_%d", vid, playType)

		// 初始化vid+playType的汇总记录
		if results[key] == nil {
			results[key] = &VidDatePlayNumSummary{
				Vid:           vid,
				PlayType:      playType,
				PlayTypeName:  playTypeInfo.Name,
				TotalPlayNum:  0,
				DailyPlayNums: make(map[string]*VidPlayNumRecord),
			}
		}

		// 创建日期记录
		dateRecord := &VidPlayNumRecord{
			Vid:          vid,
			Date:         date,
			PlayType:     playType,
			PlayTypeName: playTypeInfo.Name,
			TotalPlayNum: totalPlayNum,
			RecordCount:  recordCount,
		}

		// 添加到汇总
		results[key].DailyPlayNums[date] = dateRecord
		results[key].TotalPlayNum += totalPlayNum
	}

	return results, nil
}

// GetPlayNumByVidsAndSpecificDate 根据vid数组和指定日期查询播放量（按play_type分组）
func GetPlayNumByVidsAndSpecificDate(vids []string, targetDate string) (map[string]*VidPlayNumRecord, error) {
	// 获取数据库连接
	db, err := CreateOptimizedDBConnection()
	if err != nil {
		return nil, fmt.Errorf("error creating database connection: %w", err)
	}
	defer db.Close()

	// 将日期转换为时间戳范围（当天的开始和结束）
	startTimestamp, endTimestamp, err := convertSingleDateToTimestamps(targetDate)
	if err != nil {
		return nil, fmt.Errorf("error converting date: %w", err)
	}

	// 构建查询语句
	vidParams := strings.Repeat("?,", len(vids))
	vidParams = vidParams[:len(vidParams)-1] // 移除最后一个逗号

	query := fmt.Sprintf(`
		SELECT
			vid,
			play_type,
			SUM(play_num) as total_play_num,
			COUNT(*) as record_count
		FROM song_play_record
		WHERE vid IN (%s)
		AND start_time >= ? AND start_time < ?
		GROUP BY vid, play_type
		ORDER BY vid, play_type
	`, vidParams)

	// 准备参数
	args := make([]interface{}, 0, len(vids)+2)
	for _, vid := range vids {
		args = append(args, vid)
	}
	args = append(args, startTimestamp, endTimestamp)

	fmt.Printf("Executing query for %d vids on date %s\n", len(vids), targetDate)

	// 执行查询
	rows, err := db.Query(query, args...)
	if err != nil {
		return nil, fmt.Errorf("error executing query: %w", err)
	}
	defer rows.Close()

	// 解析结果
	results := make(map[string]*VidPlayNumRecord)

	for rows.Next() {
		var vid string
		var playType int
		var totalPlayNum int64
		var recordCount int

		err := rows.Scan(&vid, &playType, &totalPlayNum, &recordCount)
		if err != nil {
			return nil, fmt.Errorf("error scanning row: %w", err)
		}

		// 获取播放类型信息
		playTypeInfo := GetPlayTypeInfo(playType)

		// 创建唯一键：vid + play_type
		key := fmt.Sprintf("%s_%d", vid, playType)

		results[key] = &VidPlayNumRecord{
			Vid:          vid,
			Date:         targetDate,
			PlayType:     playType,
			PlayTypeName: playTypeInfo.Name,
			TotalPlayNum: totalPlayNum,
			RecordCount:  recordCount,
		}
	}

	return results, nil
}

// convertDateRangeToTimestamps 将日期范围转换为时间戳
func convertDateRangeToTimestamps(startDate, endDate string) (int64, int64, error) {
	// 解析开始日期
	startTime, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid start date format: %w", err)
	}

	// 解析结束日期
	endTime, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid end date format: %w", err)
	}

	// 设置为UTC时区
	startTime = startTime.UTC()
	endTime = endTime.UTC()

	// 结束时间设置为当天的23:59:59
	endTime = endTime.Add(24*time.Hour - time.Second)

	// 转换为秒级时间戳
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	return startTimestamp, endTimestamp, nil
}

// convertSingleDateToTimestamps 将单个日期转换为当天的开始和结束时间戳
func convertSingleDateToTimestamps(date string) (int64, int64, error) {
	// 解析日期
	targetTime, err := time.Parse("2006-01-02", date)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid date format: %w", err)
	}

	// 设置为UTC时区
	targetTime = targetTime.UTC()

	// 当天开始时间 (00:00:00)
	startTime := targetTime
	// 当天结束时间 (下一天的00:00:00)
	endTime := targetTime.Add(24 * time.Hour)

	// 转换为秒级时间戳
	startTimestamp := startTime.Unix()
	endTimestamp := endTime.Unix()

	return startTimestamp, endTimestamp, nil
}

// ExportVidPlayNumByDateRange 导出vid按日期范围的播放量到Excel（详细版本）
func ExportVidPlayNumByDateRange(results map[string]*VidDatePlayNumSummary, outputPath string, startDate, endDate string) error {
	// 创建 Excel 文件
	f := excelize.NewFile()

	// 获取默认工作表名称
	sheetName := f.GetSheetName(0)

	// 设置表头（增加PlayType相关列）
	headers := []string{"Vid", "PlayType", "PlayTypeName", "Date", "PlayNum", "TotalPlayNum"}
	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// 写入数据
	row := 2
	for _, summary := range results {
		// 为每个日期写入一行
		for date, record := range summary.DailyPlayNums {
			f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), record.Vid)
			f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), record.PlayType)
			f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), record.PlayTypeName)
			f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), date)
			f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), record.TotalPlayNum)
			f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), summary.TotalPlayNum)
			row++
		}
	}

	// 设置列宽
	f.SetColWidth(sheetName, "A", "A", 20) // Vid
	f.SetColWidth(sheetName, "B", "B", 10) // PlayType
	f.SetColWidth(sheetName, "C", "C", 12) // PlayTypeName
	f.SetColWidth(sheetName, "D", "D", 12) // Date
	f.SetColWidth(sheetName, "E", "E", 12) // PlayNum
	f.SetColWidth(sheetName, "F", "F", 15) // TotalPlayNum

	// 保存文件
	return f.SaveAs(outputPath)
}

// ExportVidPlayNumSummaryByDateRange 导出vid按日期范围的播放量汇总到Excel
func ExportVidPlayNumSummaryByDateRange(results map[string]*VidDatePlayNumSummary, outputPath string) error {
	// 创建 Excel 文件
	f := excelize.NewFile()

	// 获取默认工作表名称
	sheetName := f.GetSheetName(0)

	// 设置表头（增加PlayType相关列）
	f.SetCellValue(sheetName, "A1", "Vid")
	f.SetCellValue(sheetName, "B1", "PlayType")
	f.SetCellValue(sheetName, "C1", "PlayTypeName")
	f.SetCellValue(sheetName, "D1", "TotalPlayNum")

	// 写入数据
	row := 2
	for _, summary := range results {
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), summary.Vid)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), summary.PlayType)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), summary.PlayTypeName)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), summary.TotalPlayNum)
		row++
	}

	// 设置列宽
	f.SetColWidth(sheetName, "A", "A", 20) // Vid
	f.SetColWidth(sheetName, "B", "B", 10) // PlayType
	f.SetColWidth(sheetName, "C", "C", 12) // PlayTypeName
	f.SetColWidth(sheetName, "D", "D", 15) // TotalPlayNum

	// 保存文件
	return f.SaveAs(outputPath)
}

// ExportVidPlayNumBySpecificDate 导出vid在指定日期的播放量到Excel
func ExportVidPlayNumBySpecificDate(results map[string]*VidPlayNumRecord, outputPath string, targetDate string) error {
	// 创建 Excel 文件
	f := excelize.NewFile()

	// 获取默认工作表名称
	sheetName := f.GetSheetName(0)

	// 设置表头（增加PlayType相关列）
	f.SetCellValue(sheetName, "A1", "Vid")
	f.SetCellValue(sheetName, "B1", "PlayType")
	f.SetCellValue(sheetName, "C1", "PlayTypeName")
	f.SetCellValue(sheetName, "D1", "Date")
	f.SetCellValue(sheetName, "E1", "PlayNum")

	// 写入数据
	row := 2
	for _, record := range results {
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), record.Vid)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), record.PlayType)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), record.PlayTypeName)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), record.Date)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), record.TotalPlayNum)
		row++
	}

	// 设置列宽
	f.SetColWidth(sheetName, "A", "A", 20) // Vid
	f.SetColWidth(sheetName, "B", "B", 10) // PlayType
	f.SetColWidth(sheetName, "C", "C", 12) // PlayTypeName
	f.SetColWidth(sheetName, "D", "D", 12) // Date
	f.SetColWidth(sheetName, "E", "E", 15) // PlayNum

	// 保存文件
	return f.SaveAs(outputPath)
}

// ExportVidPlayNumByPlayType 按PlayType分别导出文件，同时导出综合详情
func ExportVidPlayNumByPlayType(results map[string]*VidDatePlayNumSummary, outputDir, baseFileName string) error {
	// 1. 先导出综合详情文件（包含所有PlayType的数据）
	allDetailFileName := fmt.Sprintf("ALL_%s_daily_details.xlsx", baseFileName)
	allDetailOutputPath := filepath.Join(outputDir, allDetailFileName)

	fmt.Printf("导出综合详细结果到: %s\n", allDetailOutputPath)
	err := ExportVidPlayNumByDateRange(results, allDetailOutputPath, "", "")
	if err != nil {
		return fmt.Errorf("error exporting all detailed results: %w", err)
	}

	// 2. 导出综合汇总文件（包含所有PlayType的汇总）
	allSummaryFileName := fmt.Sprintf("ALL_%s_summary.xlsx", baseFileName)
	allSummaryOutputPath := filepath.Join(outputDir, allSummaryFileName)

	fmt.Printf("导出综合汇总结果到: %s\n", allSummaryOutputPath)
	err = ExportVidPlayNumSummaryByDateRange(results, allSummaryOutputPath)
	if err != nil {
		return fmt.Errorf("error exporting all summary results: %w", err)
	}

	// 3. 按PlayType分组
	playTypeGroups := make(map[int]map[string]*VidDatePlayNumSummary)

	for key, summary := range results {
		playType := summary.PlayType
		if playTypeGroups[playType] == nil {
			playTypeGroups[playType] = make(map[string]*VidDatePlayNumSummary)
		}
		playTypeGroups[playType][key] = summary
	}

	// 4. 为每个PlayType导出单独的文件
	for playType, groupResults := range playTypeGroups {
		playTypeInfo := GetPlayTypeInfo(playType)

		// 详细文件
		detailFileName := fmt.Sprintf("%s%s_daily_%s.xlsx", playTypeInfo.Prefix, baseFileName, playTypeInfo.IsClip)
		detailOutputPath := filepath.Join(outputDir, detailFileName)

		fmt.Printf("导出%s详细结果到: %s\n", playTypeInfo.Name, detailOutputPath)
		err := ExportVidPlayNumByDateRange(groupResults, detailOutputPath, "", "")
		if err != nil {
			return fmt.Errorf("error exporting %s detailed results: %w", playTypeInfo.Name, err)
		}

		// 汇总文件
		summaryFileName := fmt.Sprintf("%s%s_summary_%s.xlsx", playTypeInfo.Prefix, baseFileName, playTypeInfo.IsClip)
		summaryOutputPath := filepath.Join(outputDir, summaryFileName)

		fmt.Printf("导出%s汇总结果到: %s\n", playTypeInfo.Name, summaryOutputPath)
		err = ExportVidPlayNumSummaryByDateRange(groupResults, summaryOutputPath)
		if err != nil {
			return fmt.Errorf("error exporting %s summary results: %w", playTypeInfo.Name, err)
		}

		// 打印该PlayType的统计信息
		totalPlayNum := int64(0)
		totalDays := 0
		for _, summary := range groupResults {
			totalPlayNum += summary.TotalPlayNum
			totalDays += len(summary.DailyPlayNums)
		}
		fmt.Printf("%s统计: PlayNum=%d, Days=%d, Vids=%d\n",
			playTypeInfo.Name, totalPlayNum, totalDays, len(groupResults))
	}

	return nil
}

// ExportVidPlayNumByPlayTypeForSpecificDate 按PlayType分别导出指定日期的文件，同时导出综合详情
func ExportVidPlayNumByPlayTypeForSpecificDate(results map[string]*VidPlayNumRecord, outputDir, baseFileName string) error {
	// 1. 先导出综合详情文件（包含所有PlayType的数据）
	allFileName := fmt.Sprintf("ALL_%s_details.xlsx", baseFileName)
	allOutputPath := filepath.Join(outputDir, allFileName)

	fmt.Printf("导出综合详细结果到: %s\n", allOutputPath)

	// 获取日期（从第一个记录中获取）
	var targetDate string
	for _, record := range results {
		targetDate = record.Date
		break
	}

	err := ExportVidPlayNumBySpecificDate(results, allOutputPath, targetDate)
	if err != nil {
		return fmt.Errorf("error exporting all results: %w", err)
	}

	// 2. 按PlayType分组
	playTypeGroups := make(map[int]map[string]*VidPlayNumRecord)

	for key, record := range results {
		playType := record.PlayType
		if playTypeGroups[playType] == nil {
			playTypeGroups[playType] = make(map[string]*VidPlayNumRecord)
		}
		playTypeGroups[playType][key] = record
	}

	// 3. 为每个PlayType导出单独的文件
	for playType, groupResults := range playTypeGroups {
		playTypeInfo := GetPlayTypeInfo(playType)

		fileName := fmt.Sprintf("%s%s_%s.xlsx", playTypeInfo.Prefix, baseFileName, playTypeInfo.IsClip)
		outputPath := filepath.Join(outputDir, fileName)

		fmt.Printf("导出%s结果到: %s\n", playTypeInfo.Name, outputPath)

		err := ExportVidPlayNumBySpecificDate(groupResults, outputPath, targetDate)
		if err != nil {
			return fmt.Errorf("error exporting %s results: %w", playTypeInfo.Name, err)
		}

		// 打印该PlayType的统计信息
		totalPlayNum := int64(0)
		for _, record := range groupResults {
			totalPlayNum += record.TotalPlayNum
		}
		fmt.Printf("%s统计: PlayNum=%d, Vids=%d\n",
			playTypeInfo.Name, totalPlayNum, len(groupResults))
	}

	return nil
}

// VidDetailRecord 表示vid的详细记录
type VidDetailRecord struct {
	ID           int64
	Vid          string
	PlayType     int
	PlayTypeName string
	StartTime    int64
	StartTimeUTC string
	PlayNum      int64
	SongCode     string
	UniqueId     string
	Lts          int64
	LtsUTC       string
}

// GetDetailRecordsByVidAndDateRange 获取指定vid在日期范围内的所有详细记录
func GetDetailRecordsByVidAndDateRange(vid string, startDate, endDate string) ([]VidDetailRecord, error) {
	// 获取数据库连接
	db, err := CreateOptimizedDBConnection()
	if err != nil {
		return nil, fmt.Errorf("error creating database connection: %w", err)
	}
	defer db.Close()

	// 将日期转换为时间戳范围
	startTimestamp, endTimestamp, err := convertDateRangeToTimestamps(startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("error converting date range: %w", err)
	}

	query := `
		SELECT
			id,
			vid,
			play_type,
			start_time,
			play_num,
			song_code,
			unique_id,
			lts
		FROM song_play_record
		WHERE vid = ?
		AND start_time >= ? AND start_time <= ?
		ORDER BY start_time, play_type, id
	`

	fmt.Printf("Executing detail query for vid %s from %s to %s\n", vid, startDate, endDate)

	// 执行查询
	rows, err := db.Query(query, vid, startTimestamp, endTimestamp)
	if err != nil {
		return nil, fmt.Errorf("error executing query: %w", err)
	}
	defer rows.Close()

	// 解析结果
	var results []VidDetailRecord

	for rows.Next() {
		var record VidDetailRecord

		err := rows.Scan(
			&record.ID,
			&record.Vid,
			&record.PlayType,
			&record.StartTime,
			&record.PlayNum,
			&record.SongCode,
			&record.UniqueId,
			&record.Lts,
		)
		if err != nil {
			return nil, fmt.Errorf("error scanning row: %w", err)
		}

		// 获取播放类型信息
		playTypeInfo := GetPlayTypeInfo(record.PlayType)
		record.PlayTypeName = playTypeInfo.Name

		// 转换时间戳为UTC时间字符串
		record.StartTimeUTC = time.Unix(record.StartTime, 0).UTC().Format("2006-01-02 15:04:05")
		record.LtsUTC = time.Unix(record.Lts/1000, (record.Lts%1000)*1000000).UTC().Format("2006-01-02 15:04:05.000")

		results = append(results, record)
	}

	return results, nil
}

// ExportVidDetailRecordsByPlayType 按PlayType分别导出vid的详细记录
func ExportVidDetailRecordsByPlayType(records []VidDetailRecord, outputDir, baseFileName string) error {
	if len(records) == 0 {
		return fmt.Errorf("no records to export")
	}

	// 1. 导出所有记录的综合文件
	allFileName := fmt.Sprintf("ALL_%s_detail_records.xlsx", baseFileName)
	allOutputPath := filepath.Join(outputDir, allFileName)

	fmt.Printf("导出所有详细记录到: %s\n", allOutputPath)
	err := ExportVidDetailRecordsToExcel(records, allOutputPath)
	if err != nil {
		return fmt.Errorf("error exporting all detail records: %w", err)
	}

	// 2. 按PlayType分组
	playTypeGroups := make(map[int][]VidDetailRecord)

	for _, record := range records {
		playTypeGroups[record.PlayType] = append(playTypeGroups[record.PlayType], record)
	}

	// 3. 为每个PlayType导出单独的文件
	for playType, groupRecords := range playTypeGroups {
		playTypeInfo := GetPlayTypeInfo(playType)

		fileName := fmt.Sprintf("%s%s_detail_records_%s.xlsx", playTypeInfo.Prefix, baseFileName, playTypeInfo.IsClip)
		outputPath := filepath.Join(outputDir, fileName)

		fmt.Printf("导出%s详细记录到: %s\n", playTypeInfo.Name, outputPath)

		err := ExportVidDetailRecordsToExcel(groupRecords, outputPath)
		if err != nil {
			return fmt.Errorf("error exporting %s detail records: %w", playTypeInfo.Name, err)
		}

		// 打印该PlayType的统计信息
		totalPlayNum := int64(0)
		for _, record := range groupRecords {
			totalPlayNum += record.PlayNum
		}
		fmt.Printf("%s统计: Records=%d, Total PlayNum=%d\n",
			playTypeInfo.Name, len(groupRecords), totalPlayNum)
	}

	return nil
}

// ExportVidDetailRecordsToExcel 导出详细记录到Excel
func ExportVidDetailRecordsToExcel(records []VidDetailRecord, outputPath string) error {
	// 创建 Excel 文件
	f := excelize.NewFile()

	// 获取默认工作表名称
	sheetName := f.GetSheetName(0)

	// 设置表头
	headers := []string{
		"ID", "Vid", "PlayType", "PlayTypeName", "StartTime", "StartTimeUTC",
		"PlayNum", "SongCode", "UniqueId", "Lts", "LtsUTC",
	}
	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// 写入数据
	for i, record := range records {
		row := i + 2
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), record.ID)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), record.Vid)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), record.PlayType)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), record.PlayTypeName)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), record.StartTime)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), record.StartTimeUTC)
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), record.PlayNum)
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), record.SongCode)
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", row), record.UniqueId)
		f.SetCellValue(sheetName, fmt.Sprintf("J%d", row), record.Lts)
		f.SetCellValue(sheetName, fmt.Sprintf("K%d", row), record.LtsUTC)
	}

	// 设置列宽
	f.SetColWidth(sheetName, "A", "A", 12) // ID
	f.SetColWidth(sheetName, "B", "B", 15) // Vid
	f.SetColWidth(sheetName, "C", "C", 10) // PlayType
	f.SetColWidth(sheetName, "D", "D", 12) // PlayTypeName
	f.SetColWidth(sheetName, "E", "E", 15) // StartTime
	f.SetColWidth(sheetName, "F", "F", 20) // StartTimeUTC
	f.SetColWidth(sheetName, "G", "G", 12) // PlayNum
	f.SetColWidth(sheetName, "H", "H", 20) // SongCode
	f.SetColWidth(sheetName, "I", "I", 25) // UniqueId
	f.SetColWidth(sheetName, "J", "J", 15) // Lts
	f.SetColWidth(sheetName, "K", "K", 20) // LtsUTC

	// 保存文件
	return f.SaveAs(outputPath)
}

// ExportVidPlayNumByDateEnter 主入口函数：根据vid数组和日期查询播放量
func ExportVidPlayNumByDateEnter() error {
	// 示例vid数组
	vids := []string{"1484085"}

	fmt.Println("请选择查询模式：")
	fmt.Println("1. 查询指定日期的播放量")
	fmt.Println("2. 查询日期范围的播放量")
	fmt.Print("请输入选项 (1 或 2): ")

	var choice int
	_, err := fmt.Scanf("%d", &choice)
	if err != nil {
		return fmt.Errorf("输入错误: %w", err)
	}

	// 创建输出目录
	outputDir := "output"
	if _, err := os.Stat(outputDir); os.IsNotExist(err) {
		err = os.Mkdir(outputDir, 0755)
		if err != nil {
			return fmt.Errorf("error creating output directory: %w", err)
		}
	}

	timestamp := time.Now().Format("20060102_150405")

	switch choice {
	case 1:
		// 查询指定日期
		fmt.Print("请输入查询日期 (格式: 2006-01-02): ")
		var targetDate string
		_, err := fmt.Scanf("%s", &targetDate)
		if err != nil {
			return fmt.Errorf("日期输入错误: %w", err)
		}

		fmt.Printf("开始查询 %d 个vid在 %s 的播放量...\n", len(vids), targetDate)

		results, err := GetPlayNumByVidsAndSpecificDate(vids, targetDate)
		if err != nil {
			return fmt.Errorf("error querying play numbers: %w", err)
		}

		fmt.Printf("查询完成，找到 %d 个vid的数据\n", len(results))

		// 输出统计信息
		totalPlayNum := int64(0)
		totalRecords := 0
		for vid, record := range results {
			fmt.Printf("VID: %s, PlayNum: %d, Records: %d\n",
				vid, record.TotalPlayNum, record.RecordCount)
			totalPlayNum += record.TotalPlayNum
			totalRecords += record.RecordCount
		}
		fmt.Printf("总计: PlayNum=%d, Records=%d\n", totalPlayNum, totalRecords)

		// 按PlayType分别导出结果
		baseFileName := fmt.Sprintf("vid_playnum_date_%s_%s",
			strings.ReplaceAll(targetDate, "-", ""), timestamp)

		err = ExportVidPlayNumByPlayTypeForSpecificDate(results, outputDir, baseFileName)
		if err != nil {
			return fmt.Errorf("error exporting results by play type: %w", err)
		}

		// 打印统计信息（参考 ExportSongCodePlayNumFromFile.go 的格式）
		fmt.Println("\n=== Statistics Summary ===")
		for key, record := range results {
			fmt.Printf("Key: %s, Vid: %s, PlayType: %s, PlayNum: %d\n",
				key, record.Vid, record.PlayTypeName, record.TotalPlayNum)
		}
		fmt.Printf("Overall Total PlayNum: %d\n", totalPlayNum)

	case 2:
		// 查询日期范围
		fmt.Print("请输入开始日期 (格式: 2006-01-02): ")
		var startDate string
		_, err := fmt.Scanf("%s", &startDate)
		if err != nil {
			return fmt.Errorf("开始日期输入错误: %w", err)
		}

		fmt.Print("请输入结束日期 (格式: 2006-01-02): ")
		var endDate string
		_, err = fmt.Scanf("%s", &endDate)
		if err != nil {
			return fmt.Errorf("结束日期输入错误: %w", err)
		}

		fmt.Printf("开始查询 %d 个vid从 %s 到 %s 的播放量...\n", len(vids), startDate, endDate)

		results, err := GetPlayNumByVidsAndDateRange(vids, startDate, endDate)
		if err != nil {
			return fmt.Errorf("error querying play numbers: %w", err)
		}

		fmt.Printf("查询完成，找到 %d 个vid的数据\n", len(results))

		// 输出统计信息
		totalPlayNum := int64(0)
		totalDays := 0
		for vid, summary := range results {
			fmt.Printf("VID: %s, Total PlayNum: %d, Days: %d\n",
				vid, summary.TotalPlayNum, len(summary.DailyPlayNums))
			totalPlayNum += summary.TotalPlayNum
			totalDays += len(summary.DailyPlayNums)
		}
		fmt.Printf("总计: PlayNum=%d, Total Days=%d\n", totalPlayNum, totalDays)

		// 按PlayType分别导出结果
		baseFileName := fmt.Sprintf("vid_playnum_%s_to_%s_%s",
			strings.ReplaceAll(startDate, "-", ""), strings.ReplaceAll(endDate, "-", ""), timestamp)

		err = ExportVidPlayNumByPlayType(results, outputDir, baseFileName)
		if err != nil {
			return fmt.Errorf("error exporting results by play type: %w", err)
		}

	default:
		return fmt.Errorf("无效的选项，请输入 1 或 2")
	}

	fmt.Println("Export completed successfully!")
	return nil
}
