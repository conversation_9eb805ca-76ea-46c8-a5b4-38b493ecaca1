package main

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"os"
	"path/filepath"
	"time"
)

// VidSceneTypeRecord 表示vid和sceneType的记录
type VidSceneTypeRecord struct {
	Vid       string
	SceneType int
	Count     int64 // 该vid在该sceneType下的记录数量
}

// GetVidsBySceneType 从数据库查询指定sceneType的所有vid
func GetVidsBySceneType(sceneTypes []int) ([]VidSceneTypeRecord, error) {
	// 获取数据库连接
	db, err := CreateOptimizedDBConnection()
	if err != nil {
		return nil, fmt.Errorf("error creating database connection: %w", err)
	}
	defer db.Close()

	// 构建查询语句 - 查询指定sceneType的vid及其记录数量
	query := `
		SELECT 
			vid,
			scene_type,
			COUNT(*) as record_count
		FROM song_play_record 
		WHERE scene_type IN (1, 2, 5)
		GROUP BY vid, scene_type
		ORDER BY vid, scene_type
	`

	fmt.Printf("Executing query to get vids with scene_type in (1, 2, 5)\n")
	fmt.Printf("Query: %s\n", query)

	// 执行查询
	rows, err := db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("error executing query: %w", err)
	}
	defer rows.Close()

	// 解析结果
	var results []VidSceneTypeRecord

	for rows.Next() {
		var record VidSceneTypeRecord

		err := rows.Scan(&record.Vid, &record.SceneType, &record.Count)
		if err != nil {
			return nil, fmt.Errorf("error scanning row: %w", err)
		}

		results = append(results, record)
	}

	return results, nil
}

// GetDistinctVidsBySceneType 获取指定sceneType的不重复vid列表
func GetDistinctVidsBySceneType(sceneTypes []int) ([]string, error) {
	// 获取数据库连接
	db, err := CreateOptimizedDBConnection()
	if err != nil {
		return nil, fmt.Errorf("error creating database connection: %w", err)
	}
	defer db.Close()

	// 构建查询语句 - 查询指定sceneType的不重复vid
	query := `
		SELECT DISTINCT vid
		FROM song_play_record 
		WHERE scene_type IN (1, 2, 5)
		ORDER BY vid
	`

	fmt.Printf("Executing query to get distinct vids with scene_type in (1, 2, 5)\n")
	fmt.Printf("Query: %s\n", query)

	// 执行查询
	rows, err := db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("error executing query: %w", err)
	}
	defer rows.Close()

	// 解析结果
	var results []string

	for rows.Next() {
		var vid string

		err := rows.Scan(&vid)
		if err != nil {
			return nil, fmt.Errorf("error scanning row: %w", err)
		}

		results = append(results, vid)
	}

	return results, nil
}

// ExportVidsBySceneTypeToExcel 将结果导出到Excel文件
func ExportVidsBySceneTypeToExcel(records []VidSceneTypeRecord, outputPath string) error {
	// 创建新的Excel文件
	f := excelize.NewFile()

	// 获取默认工作表名称
	sheetName := f.GetSheetName(0)

	// 定义表头
	headers := []string{"vid", "scene_type", "record_count"}

	// 写入表头
	for colIdx, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(colIdx+1, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// 写入数据
	for rowIdx, record := range records {
		row := rowIdx + 2 // 从第2行开始写入数据（第1行是表头）

		// vid
		cell, _ := excelize.CoordinatesToCellName(1, row)
		f.SetCellValue(sheetName, cell, record.Vid)

		// scene_type
		cell, _ = excelize.CoordinatesToCellName(2, row)
		f.SetCellValue(sheetName, cell, record.SceneType)

		// record_count
		cell, _ = excelize.CoordinatesToCellName(3, row)
		f.SetCellValue(sheetName, cell, record.Count)
	}

	// 保存文件
	err := f.SaveAs(outputPath)
	if err != nil {
		return fmt.Errorf("error saving Excel file: %w", err)
	}

	return nil
}

// ExportDistinctVidsToExcel 将不重复的vid列表导出到Excel文件
func ExportDistinctVidsToExcel(vids []string, outputPath string) error {
	// 创建新的Excel文件
	f := excelize.NewFile()

	// 获取默认工作表名称
	sheetName := f.GetSheetName(0)

	// 定义表头
	headers := []string{"vid"}

	// 写入表头
	for colIdx, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(colIdx+1, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// 写入数据
	for rowIdx, vid := range vids {
		row := rowIdx + 2 // 从第2行开始写入数据（第1行是表头）

		// vid
		cell, _ := excelize.CoordinatesToCellName(1, row)
		f.SetCellValue(sheetName, cell, vid)
	}

	// 保存文件
	err := f.SaveAs(outputPath)
	if err != nil {
		return fmt.Errorf("error saving Excel file: %w", err)
	}

	return nil
}

// ExportVidsBySceneTypeEnter 导出sceneType为1,2,5的vid的主入口函数
func ExportVidsBySceneTypeEnter() error {
	fmt.Println("开始导出 sceneType 为 1, 2, 5 的 vid...")

	// 指定要查询的sceneType
	sceneTypes := []int{1, 2, 5}

	// 创建输出目录
	outputDir := "output"
	err := createOutputDir(outputDir)
	if err != nil {
		return fmt.Errorf("error creating output directory: %w", err)
	}

	// 生成时间戳用于文件名
	timestamp := time.Now().Format("20060102_150405")

	// 1. 导出详细记录（包含vid, scene_type, record_count）
	fmt.Println("正在查询详细记录...")
	detailRecords, err := GetVidsBySceneType(sceneTypes)
	if err != nil {
		return fmt.Errorf("error getting vids by scene type: %w", err)
	}

	detailOutputPath := filepath.Join(outputDir, fmt.Sprintf("vids_by_scene_type_detail_%s.xlsx", timestamp))
	fmt.Printf("导出详细记录到: %s\n", detailOutputPath)
	err = ExportVidsBySceneTypeToExcel(detailRecords, detailOutputPath)
	if err != nil {
		return fmt.Errorf("error exporting detail records: %w", err)
	}

	fmt.Printf("详细记录导出完成，共 %d 条记录\n", len(detailRecords))

	// 2. 导出不重复的vid列表
	fmt.Println("正在查询不重复的vid列表...")
	distinctVids, err := GetDistinctVidsBySceneType(sceneTypes)
	if err != nil {
		return fmt.Errorf("error getting distinct vids: %w", err)
	}

	distinctOutputPath := filepath.Join(outputDir, fmt.Sprintf("distinct_vids_scene_type_125_%s.xlsx", timestamp))
	fmt.Printf("导出不重复vid列表到: %s\n", distinctOutputPath)
	err = ExportDistinctVidsToExcel(distinctVids, distinctOutputPath)
	if err != nil {
		return fmt.Errorf("error exporting distinct vids: %w", err)
	}

	fmt.Printf("不重复vid列表导出完成，共 %d 个不同的vid\n", len(distinctVids))

	// 打印统计信息
	fmt.Println("\n=== 统计信息 ===")
	fmt.Printf("总记录数: %d\n", len(detailRecords))
	fmt.Printf("不重复vid数: %d\n", len(distinctVids))

	// 按sceneType分组统计
	sceneTypeStats := make(map[int]int)
	for _, record := range detailRecords {
		sceneTypeStats[record.SceneType]++
	}

	fmt.Println("\n按sceneType分组统计:")
	for sceneType, count := range sceneTypeStats {
		fmt.Printf("SceneType %d: %d 条记录\n", sceneType, count)
	}

	fmt.Println("\n导出完成！")
	return nil
}

// createOutputDir 创建输出目录（如果不存在）
func createOutputDir(dir string) error {
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		err = os.Mkdir(dir, 0755)
		if err != nil {
			return fmt.Errorf("error creating directory %s: %w", dir, err)
		}
	}
	return nil
}
